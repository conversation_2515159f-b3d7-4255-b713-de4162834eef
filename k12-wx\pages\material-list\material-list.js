Page({
  data: {
    // 页面基本信息
    pageTitle: '',
    grade: '',
    category: '',
    
    // 筛选条件
    selectedSubject: '',
    selectedVolume: '',
    selectedType: '',
    selectedSort: '最新',
    selectedFilters: [],
    hasSelectedFilters: false,
    
    // 筛选弹窗
    showFilterModal: false,
    currentFilterType: '',
    currentFilterTitle: '',
    currentFilterOptions: [],
    
    // 资料列表
    materialList: [],
    totalCount: 0,
    loading: false,
    page: 1,
    hasMore: true,
    
    // 筛选选项配置 - 改为动态加载
    gradeOptions: [],
    subjectOptions: [],
    volumeOptions: [],
    typeOptions: [],
    
    sortOptions: [
      { value: 'latest', text: '最新' },
      { value: 'popular', text: '最热' },
      { value: 'download', text: '下载量' },
      { value: 'price_low', text: '积分从低到高' },
      { value: 'price_high', text: '积分从高到低' }
    ]
  },

  onLoad(options) {
    // 获取页面参数
    const { grade, category, title, sortBy } = options;
    
    this.setData({
      grade: grade || '',
      category: category || '',
      pageTitle: title || '资料列表',
      sortBy: sortBy || '' // 添加排序参数支持
    });
    
    // 先加载筛选选项，再初始化筛选条件和加载资料列表
    this.loadFilterOptions().then(() => {
      // 根据参数设置默认筛选条件
      this.initFilters(options);
      
      // 加载资料列表
      this.loadMaterialList();
    });
  },

  // 动态加载筛选选项
  async loadFilterOptions() {
    try {
      console.log('开始加载筛选选项...');
      
      // 调用新的云函数获取筛选选项
      const res = await wx.cloud.callFunction({
        name: 'material-getFilterOptions',
        data: {}
      });
      
      console.log('获取筛选选项结果:', res);
      
      if (res.result && res.result.success) {
        const data = res.result.data || {};
        
        // 构建筛选选项，适配新的数据格式，添加安全检查
        const gradeOptions = [
          { value: '', text: '全部年级' },
          ...(data.grades || []).map(grade => ({ 
            value: grade.name || grade, 
            text: grade.name || grade 
          }))
        ];
        
        const subjectOptions = [
          { value: '', text: '全部科目' },
          ...(data.subjects || []).map(subject => ({ 
            value: subject.name || subject, 
            text: subject.name || subject 
          }))
        ];
        
        const volumeOptions = [
          { value: '', text: '全部册别' },
          ...(data.volumes || []).map(volume => ({ 
            value: volume.name || volume, 
            text: volume.name || volume 
          }))
        ];
        
        const typeOptions = [
          { value: '', text: '全部板块' },
          ...(data.sections || []).map(section => ({ 
            value: section.name || section, 
            text: section.name || section 
          }))
        ];
        
        // 更新到页面数据
        this.setData({
          gradeOptions,
          subjectOptions,
          volumeOptions,
          typeOptions
        });
        
        console.log('筛选选项加载完成:', {
          gradeOptions,
          subjectOptions,
          volumeOptions,
          typeOptions
        });
        
      } else {
        throw new Error(res.result?.message || '获取筛选选项失败');
      }
      
    } catch (error) {
      console.error('加载筛选选项失败:', error);
      
      // 使用默认选项作为降级方案
      this.setData({
        gradeOptions: [
          { value: '', text: '全部年级' },
          { value: '一年级', text: '一年级' },
          { value: '二年级', text: '二年级' },
          { value: '三年级', text: '三年级' },
          { value: '四年级', text: '四年级' },
          { value: '五年级', text: '五年级' },
          { value: '六年级', text: '六年级' }
        ],
        subjectOptions: [
          { value: '', text: '全部科目' },
          { value: '语文', text: '语文' },
          { value: '数学', text: '数学' },
          { value: '英语', text: '英语' },
          { value: '科学', text: '科学' }
        ],
        volumeOptions: [
          { value: '', text: '全部册别' },
          { value: '上册', text: '上册' },
          { value: '下册', text: '下册' },
          { value: '全册', text: '全册' }
        ],
        typeOptions: [
          { value: '', text: '全部板块' },
          { value: '素材资源', text: '素材资源' },
          { value: '专项练习', text: '专项练习' },
          { value: '单元同步', text: '单元同步' },
          { value: '教案学案', text: '教案学案' }
        ]
      });
    }
  },

  // 初始化筛选条件
  initFilters(options) {
    const { grade, category, subject } = options;
    let selectedFilters = [];
    
    // 如果是从年级入口进入，添加年级筛选
    if (grade) {
      selectedFilters.push({
        type: 'grade',
        text: grade,
        value: grade
      });
    }
    
    // 如果是从分类入口进入，添加学科筛选
    if (subject) {
      this.setData({
        selectedSubject: this.getSubjectText(subject)
      });
      selectedFilters.push({
        type: 'subject',
        text: this.getSubjectText(subject),
        value: subject
      });
    }
    
    this.setData({
      selectedFilters,
      hasSelectedFilters: selectedFilters.length > 0
    });
  },

  // 获取学科文本
  getSubjectText(value) {
    const option = this.data.subjectOptions.find(item => item.value === value);
    return option ? option.text : '';
  },

  // 显示筛选弹窗
  showSubjectFilter() {
    this.showFilterModal('subject', '选择科目', this.data.subjectOptions);
  },

  showVolumeFilter() {
    this.showFilterModal('volume', '选择册别', this.data.volumeOptions);
  },

  showTypeFilter() {
    this.showFilterModal('section', '选择板块', this.data.typeOptions);
  },

  showSortFilter() {
    this.showFilterModal('sort', '选择排序', this.data.sortOptions);
  },

  // 通用显示筛选弹窗方法
  showFilterModal(type, title, options) {
    const currentValue = this.getCurrentFilterValue(type);
    const processedOptions = options.map(item => ({
      ...item,
      selected: item.value === currentValue
    }));
    
    this.setData({
      showFilterModal: true,
      currentFilterType: type,
      currentFilterTitle: title,
      currentFilterOptions: processedOptions
    });
  },

  // 获取当前筛选值
  getCurrentFilterValue(type) {
    switch (type) {
      case 'subject': return this.getFilterValue('subject');
      case 'volume': return this.getFilterValue('volume');
      case 'section': return this.getFilterValue('section');
      case 'sort': return this.data.selectedSort === '最新' ? 'latest' : this.getFilterValue('sort');
      default: return '';
    }
  },

  getFilterValue(type) {
    const filter = this.data.selectedFilters.find(item => item.type === type);
    return filter ? filter.value : '';
  },

  // 选择筛选选项
  selectFilterOption(e) {
    const { value, text } = e.currentTarget.dataset;
    const { currentFilterType } = this.data;
    
    // 更新对应的筛选条件
    const updateData = {};
    updateData[`selected${currentFilterType.charAt(0).toUpperCase() + currentFilterType.slice(1)}`] = text;
    
    // 更新已选筛选条件
    let selectedFilters = this.data.selectedFilters.filter(item => item.type !== currentFilterType);
    if (value) {
      selectedFilters.push({
        type: currentFilterType,
        text: text,
        value: value
      });
    }
    
    updateData.selectedFilters = selectedFilters;
    updateData.hasSelectedFilters = selectedFilters.length > 0;
    updateData.showFilterModal = false;
    
    this.setData(updateData);
    
    // 重新加载数据
    this.loadMaterialList(true);
  },

  // 移除筛选条件
  removeFilter(e) {
    const { type } = e.currentTarget.dataset;
    let selectedFilters = this.data.selectedFilters.filter(item => item.type !== type);
    
    // 清空对应的显示文本
    const updateData = {
      selectedFilters,
      hasSelectedFilters: selectedFilters.length > 0
    };
    
    switch (type) {
      case 'subject':
        updateData.selectedSubject = '';
        break;
      case 'volume':
        updateData.selectedVolume = '';
        break;
      case 'section':
        updateData.selectedType = '';
        break;
      case 'sort':
        updateData.selectedSort = '最新';
        break;
    }
    
    this.setData(updateData);
    this.loadMaterialList(true);
  },

  // 清空所有筛选条件
  clearAllFilters() {
    this.setData({
      selectedSubject: '',
      selectedVolume: '',
      selectedType: '',
      selectedSort: '最新',
      selectedFilters: [],
      hasSelectedFilters: false
    });
    this.loadMaterialList(true);
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({
      showFilterModal: false
    });
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 加载资料列表
  async loadMaterialList(reset = false) {
    if (this.data.loading) return;
    
    const page = reset ? 1 : this.data.page;
    
    this.setData({
      loading: true,
      page: page
    });
    
    try {
      // 构建请求参数
      const params = this.buildRequestParams();
      params.page = page;
      params.pageSize = 20;
      
      // 如果是热门资料页面，添加排序参数
      if (this.data.sortBy === 'downloadCount') {
        params.sortBy = 'downloadCount';
      }
      
      console.log('调用云函数获取资料列表，参数:', params);
      
      // 调用专门的筛选云函数
      const res = await wx.cloud.callFunction({
        name: 'material-getFilteredList',
        data: params
      });
      
      console.log('云函数返回结果:', res);
      
      if (res.result && res.result.success) {
        const newList = res.result.data || [];
        let materialList = reset ? newList : [...this.data.materialList, ...newList];
        
        this.setData({
          materialList,
          totalCount: res.result.total || 0,
          loading: false,
          hasMore: res.result.hasMore || false,
          page: page + 1
        });
      } else {
        throw new Error(res.result?.message || '获取数据失败');
      }
      
    } catch (error) {
      console.error('加载资料列表失败:', error);
      
      this.setData({
        loading: false,
        hasMore: false
      });
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 构建请求参数
  buildRequestParams() {
    const params = {
      grade: this.data.grade,
      category: this.data.category
    };
    
    this.data.selectedFilters.forEach(filter => {
      params[filter.type] = filter.value;
    });
    
    return params;
  },

  // 页面事件
  goBack() {
    wx.navigateBack();
  },

  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  },

  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadMaterialList(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMaterialList();
    }
  }
});