{"name": "k12-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:force": "node scripts/start-dev.cjs", "start": "node scripts/start-dev.cjs", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@cloudbase/js-sdk": "^2.20.0", "@cloudbase/node-sdk": "^3.10.1", "@element-plus/icons-vue": "^2.3.2", "@types/crypto-js": "^4.2.2", "axios": "^1.11.0", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.10.5", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}