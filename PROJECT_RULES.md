数据接口自己在函数内部实现// 学科映射用于进一步筛选
const subjectMap = {
  // 幼升小分类映射
  'pinyin': '拼音',
  'math': '数学',
  'writing': '识字',
  'habit': '习惯',
  // 小升初分类映射
  'chinese': '语文',
  'english': '英语',
  'interview': '面试',
  'exam': '考试'
}

if (subjectMap[this.data.activeTab]) {
  queryData.subject = subjectMap[this.data.activeTab]
}


// 幼升小分类映射
  'pinyin': '拼音',
  'math': '数学',
  'writing': '识字',
  'habit': '习惯',
  // 小升初分类映射
  'chinese': '语文',
  'english': '英语',
  'interview': '面试',
  'exam': '考试'

幼升小选以下五个
  拼音启蒙
  认识数字
  习惯养成
  学科启蒙
  知识科普

小升初选以下五个
  语文冲刺
  数学冲刺
  英语强化
  面试准备
  真题模拟



  当前可正常运行的功能：
✅ 首页功能 - files + system_configs 支持
✅ 分类功能 - files 集合完全支持
✅ 搜索功能 - files + system_configs 支持
✅ 积分系统 - users + point_records 支持
✅ 下载/收藏/分享 - 对应集合已存在
受影响的功能：
❌ 用户反馈系统 - 缺少 feedbacks 集合
❌ 邀请好友功能 - 缺少 invitations 集合
❌ 每日积分限制 - 缺少 daily_point_limits 集合
💡 建议措施
优先级1（高）：

创建 daily_point_limits 集合，确保积分系统的完整性
优先级2（中）：

创建 feedbacks 集合，完善用户体验反馈渠道
创建 invitations 集合，支持用户增长功能