<view class="container">
  <!-- 资料信息 -->
  <view class="material-info">
    <image class="material-cover" src="{{materialInfo.cover}}" mode="aspectFill" />
    <view class="info-content">
      <view class="material-title">{{materialInfo.title}}</view>
      <view class="material-tags">
        <text class="tag" wx:for="{{materialInfo.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
      </view>
      <view class="material-stats">
        <view class="stat-item">
          <text class="stat-label">下载量</text>
          <text class="stat-value">{{materialInfo.downloadCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">浏览量</text>
          <text class="stat-value">{{materialInfo.viewCount}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">评分</text>
          <text class="stat-value">{{materialInfo.rating}}</text>
        </view>
      </view>
      <view class="material-price">
        <text class="price-label">所需积分：</text>
        <text class="price-value text-function">{{materialInfo.points}}</text>
      </view>
    </view>
  </view>

  <!-- 资料描述 -->
  <view class="material-description">
    <view class="section-title">资料介绍</view>
    <view class="description-content">{{materialInfo.description}}</view>
  </view>

  <!-- 资料预览 -->
  <view class="material-preview" wx:if="{{materialInfo.previewImages.length > 0}}">
    <view class="section-title">资料预览</view>
    <scroll-view class="preview-scroll" scroll-x>
      <view class="preview-list">
        <image 
          class="preview-image" 
          wx:for="{{materialInfo.previewImages}}" 
          wx:key="*this"
          src="{{item}}" 
          mode="aspectFit"
          bindtap="previewImage"
          data-index="{{index}}"
        />
      </view>
    </scroll-view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-materials" wx:if="{{relatedMaterials.length > 0}}">
    <view class="section-title">相关推荐</view>
    <view class="related-list">
      <view 
        class="related-item" 
        wx:for="{{relatedMaterials}}" 
        wx:key="id"
        bindtap="goToRelated"
        data-id="{{item.id}}"
      >
        <image class="related-cover" src="{{item.cover}}" mode="aspectFill" />
        <view class="related-info">
          <view class="related-title ellipsis-2">{{item.title}}</view>
          <view class="related-price text-function">{{item.points}}积分</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-left">
      <view class="action-btn" bindtap="toggleFavorite">
        <image class="action-icon" src="{{materialInfo.isFavorited ? '/images/icon_heart_filled.png' : '/images/icon_heart.png'}}" />
        <text class="action-text">{{materialInfo.isFavorited ? '已收藏' : '收藏'}}</text>
      </view>
      <view class="action-btn" bindtap="shareResource">
        <image class="action-icon" src="/images/icon_share.png" />
        <text class="action-text">分享</text>
      </view>
    </view>
    <view class="download-btn" bindtap="downloadResource">
      <text class="download-text">立即下载</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && !materialInfo.id}}">
    <view class="empty-icon">📄</view>
    <view class="empty-text">资料不存在</view>
  </view>
</view>