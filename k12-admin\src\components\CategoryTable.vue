<template>
  <el-table v-loading="loading" :data="data" style="width: 100%">
    <el-table-column prop="name" label="分类名称" width="200" />
    <el-table-column prop="type" label="分类类型" width="120">
      <template #default="scope">
        <el-tag :type="getTypeTagType(scope.row.type)">
          {{ getTypeLabel(scope.row.type) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="order" label="排序" width="80" />
    <el-table-column prop="status" label="状态" width="100">
      <template #default="scope">
        <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
          {{ scope.row.status === 'active' ? '启用' : '禁用' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="createTime" label="创建时间" width="180">
      <template #default="scope">
        {{ formatDate(scope.row.createTime) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150">
      <template #default="scope">
        <el-button size="small" @click="$emit('edit', scope.row)">编辑</el-button>
        <el-button size="small" type="danger" @click="$emit('delete', scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import type { Category } from '../api/category'

interface Props {
  data: Category[]
  loading: boolean
}

defineProps<Props>()
defineEmits<{
  edit: [category: Category]
  delete: [category: Category]
}>()

// 获取分类类型标签颜色
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    grade: 'primary',
    subject: 'success',
    volume: 'warning',
    section: 'info'
  }
  return typeMap[type] || 'default'
}

// 获取分类类型标签文本
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    grade: '年级',
    subject: '科目',
    volume: '册别',
    section: '板块'
  }
  return typeMap[type] || type
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}
</script>