// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, userId } = event
  
  try {
    switch (action) {
      case 'getProfile':
        // 获取用户信息
        return await getUserProfile(userId)
      case 'updateProfile':
        // 更新用户信息
        return await updateUserProfile(event)
      case 'getPoints':
        // 获取用户积分
        return await getUserPoints(userId)
      case 'login':
        // 用户登录，检查是否需要发放新用户积分
        return await handleUserLogin(event)
      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('用户操作失败:', error)
    return {
      success: false,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 获取用户信息
async function getUserProfile(userId) {
  try {
    const result = await db.collection('users').where({
      openid: userId
    }).get()
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        data: result.data[0]
      }
    } else {
      return {
        success: false,
        message: '用户不存在'
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      success: false,
      message: '获取用户信息失败',
      error: error.message
    }
  }
}

// 更新用户信息
async function updateUserProfile(event) {
  const { userId, updateData } = event
  
  const result = await db.collection('users').doc(userId).update({
    data: {
      ...updateData,
      updateTime: new Date()
    }
  })
  
  return {
    success: true,
    data: result
  }
}

// 获取用户积分
async function getUserPoints(userId) {
  const result = await db.collection('users').doc(userId).field({
    points: true
  }).get()
  
  if (result.data) {
    return {
      success: true,
      data: {
        points: result.data.points || 0
      }
    }
  } else {
    return {
      success: false,
      message: '用户不存在'
    }
  }
}

// 处理用户登录，自动发放新用户积分
async function handleUserLogin(event) {
  const { openid, userInfo } = event
  
  try {
    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userResult.data && userResult.data.length > 0) {
      // 用户已存在，返回用户信息
      const user = userResult.data[0]
      return {
        success: true,
        data: {
          user: user,
          isNewUser: false
        },
        message: '登录成功'
      }
    } else {
      // 新用户，创建用户记录并发放200积分
      const newUser = {
        openid: openid,
        nickName: userInfo?.nickName || '用户',
        avatarUrl: userInfo?.avatarUrl || '',
        points: 200, // 新用户默认200积分
        createTime: new Date(),
        updateTime: new Date()
      }
      
      // 开始事务
      const transaction = await db.startTransaction()
      
      try {
        // 创建用户记录
        await transaction.collection('users').add({
          data: newUser
        })
        
        // 添加积分历史记录
        await transaction.collection('points_history').add({
          data: {
            openid: openid,
            action: 'newUser',
            points: 200,
            description: '新用户注册奖励',
            date: new Date().toISOString().split('T')[0],
            createTime: new Date(),
            extra: {}
          }
        })
        
        // 提交事务
        await transaction.commit()
        
        return {
          success: true,
          data: {
            user: newUser,
            isNewUser: true,
            rewardPoints: 200
          },
          message: '注册成功，获得200积分新用户奖励'
        }
        
      } catch (error) {
        // 回滚事务
        await transaction.rollback()
        throw error
      }
    }
    
  } catch (error) {
    console.error('用户登录处理失败:', error)
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message
    }
  }
}
