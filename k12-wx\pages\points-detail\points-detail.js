// pages/points-detail/points-detail.js
const app = getApp()

Page({
  data: {
    currentPoints: 0,
    totalEarned: 0,
    totalSpent: 0,
    pointsHistory: [],
    loading: false,
    isEmpty: false,
    filterType: 'all', // all: 全部, earn: 获得, spend: 消费
    currentPage: 1,
    hasMore: true
  },

  onLoad() {
    this.loadPointsData()
    this.loadPointsHistory()
  },

  onShow() {
    this.loadPointsData()
  },

  // 加载积分数据
  loadPointsData() {
    try {
      const currentPoints = app.getUserPoints()
      const totalEarned = wx.getStorageSync('totalEarnedPoints') || 0
      const totalSpent = wx.getStorageSync('totalSpentPoints') || 0
      
      this.setData({
        currentPoints,
        totalEarned,
        totalSpent
      })
    } catch (error) {
      console.error('加载积分数据失败:', error)
    }
  },

  // 加载积分历史记录
  loadPointsHistory() {
    this.setData({ loading: true })
    
    try {
      // 从本地存储获取积分历史记录
      let pointsHistory = wx.getStorageSync('pointsHistory') || []
      
      
      // 按时间倒序排列
      pointsHistory.sort((a, b) => new Date(b.time) - new Date(a.time))
      
      this.setData({
        pointsHistory,
        isEmpty: pointsHistory.length === 0,
        loading: false
      })
      
    } catch (error) {
      console.error('加载积分历史失败:', error)
      this.setData({
        loading: false,
        isEmpty: true
      })
    }
  },

  // 筛选类型切换
  onFilterChange(e) {
    const filterType = e.currentTarget.dataset.type
    this.setData({ filterType })
    this.filterHistory()
  },

  // 筛选历史记录
  filterHistory() {
    try {
      let pointsHistory = wx.getStorageSync('pointsHistory') || []
      
      if (this.data.filterType !== 'all') {
        pointsHistory = pointsHistory.filter(item => item.type === this.data.filterType)
      }
      
      // 按时间倒序排列
      pointsHistory.sort((a, b) => new Date(b.time) - new Date(a.time))
      
      this.setData({
        pointsHistory,
        isEmpty: pointsHistory.length === 0
      })
      
    } catch (error) {
      console.error('筛选失败:', error)
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return time.toLocaleDateString()
    }
  },

  // 跳转到赚取积分页面
  goToEarnPoints() {
    wx.navigateTo({
      url: '/pages/earn-points/earn-points'
    })
  },

  // 跳转到积分商城（暂未实现）
  goToPointsMall() {
    wx.showToast({
      title: '积分商城即将上线',
      icon: 'none'
    })
  },

  // 查看积分规则
  viewPointsRules() {
    wx.showModal({
      title: '积分规则',
      content: '1. 新用户注册：100积分\n2. 每日签到：10积分\n3. 下载资料：20积分\n4. 分享应用：15积分\n5. 完成任务：10-50积分\n6. 积分可用于兑换高级资料',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPointsData()
    this.loadPointsHistory()
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    if (!this.data.hasMore) return
    
    // 模拟加载更多数据
    wx.showLoading({ title: '加载中...' })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none'
      })
      this.setData({ hasMore: false })
    }, 1000)
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '积分明细 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})