// utils/api.js - 云函数调用封装

// 云函数调用封装
const callCloudFunction = (name, data = {}) => {
  return new Promise((resolve, reject) => {
    wx.cloud.callFunction({
      name,
      data,
      success: (res) => {
        if (res.result.success) {
          resolve(res.result.data)
        } else {
          reject(new Error(res.result.message || '操作失败'))
        }
      },
      fail: (error) => {
        console.error(`云函数${name}调用失败:`, error)
        reject(error)
      }
    })
  })
}

// 数据库直接操作封装 - 延迟初始化
let db = null
let _ = null

// 获取数据库实例的函数
const getDB = () => {
  if (!db) {
    db = wx.cloud.database()
    _ = db.command
  }
  return { db, _ }
}

// API接口定义 - 使用云函数方式
const api = {
  // 用户相关
  user: {
    // 登录
    login: (data) => callCloudFunction('user-login', data),
    
    // 获取用户信息
    getUserInfo: (userId) => callCloudFunction('user', { action: 'getProfile', userId }),
    
    // 更新用户信息
    updateUserInfo: (data) => callCloudFunction('user', { action: 'updateUserInfo', ...data }),
    
    // 获取用户统计
    getUserStats: (userId) => callCloudFunction('user-getStats', { userId }),
    
    // 更新用户资料
    updateProfile: (userId, updateData) => callCloudFunction('user-updateProfile', { userId, updateData })
  },

  // 资料相关
  material: {
    // 获取资料列表
    getMaterialList: (params) => callCloudFunction('material-getList', params),
    
    // 获取热门资料
    getHotMaterials: (params = {}) => callCloudFunction('material-getList', { 
      ...params, 
      sortBy: 'downloadCount',
      pageSize: params.pageSize || 10
    }),
    
    // 获取资料详情
    getMaterialDetail: (id) => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getMaterialDetail',
          data: { materialId: id },
          success: (res) => {
            resolve({
              success: res.result.success,
              data: res.result.data,
              message: res.result.message
            })
          },
          fail: (error) => {
            console.error('获取资料详情失败:', error)
            reject(error)
          }
        })
      })
    },
    
    // 下载资料
    downloadMaterial: (id) => callCloudFunction('material-download', { id }),
    
    // 获取推荐资料
    getRecommendMaterials: (id) => callCloudFunction('material-getRecommend', { id })
  },

  // 搜索相关
  search: {
    // 搜索资料
    searchMaterials: (params) => callCloudFunction('search-materials', params),
    
    // 获取热门搜索
    getHotKeywords: () => callCloudFunction('search-hotKeywords'),
    
    // 获取搜索建议
    getSearchSuggestions: (keyword) => callCloudFunction('search-suggestions', { keyword })
  },

  // 收藏相关
  favorite: {
    // 添加收藏
    addFavorite: (materialId) => callCloudFunction('favorite-add', { materialId }),
    
    // 取消收藏
    removeFavorite: (materialId) => callCloudFunction('favorite-remove', { materialId }),
    
    // 获取收藏列表
    getFavoriteList: (params) => callCloudFunction('favorite-getList', params),
    
    // 批量取消收藏
    batchRemoveFavorite: (materialIds) => callCloudFunction('favorite-batchRemove', { materialIds })
  },

  // 积分相关
  points: {
    // 获取积分记录
    getPointsHistory: (params) => callCloudFunction('points-getHistory', params),
    
    // 积分操作 - 统一接口
    operate: (params) => callCloudFunction('points-operate', params),
    
    // 每日签到
    signIn: (userId) => callCloudFunction('points-operate', { userId, action: 'signin' }),
    
    // 分享获取积分
    shareForPoints: (userId, extra = {}) => callCloudFunction('points-operate', { userId, action: 'share', extra }),
    
    // 邀请好友获取积分
    inviteForPoints: (userId, extra = {}) => callCloudFunction('points-operate', { userId, action: 'invite', extra }),
    
    // 新用户奖励
    newUserReward: (userId) => callCloudFunction('points-operate', { userId, action: 'newUser' }),
    
    // 观看广告获取积分
    watchAdForPoints: (userId) => callCloudFunction('points-operate', { userId, action: 'watchAd' }),
    
    // 自定义积分奖励
    customReward: (userId, points, reason) => callCloudFunction('points-operate', { userId, action: 'custom', points, reason })
  },

  // 下载相关
  download: {
    // 获取下载列表
    getDownloadList: (params) => callCloudFunction('download-getList', params),
    
    // 添加下载记录
    addDownload: (materialId, downloadPath) => callCloudFunction('download-add', { materialId, downloadPath }),
    
    // 删除下载记录
    removeDownload: (downloadId, materialId) => callCloudFunction('download-remove', { downloadId, materialId })
  },

  // 反馈相关
  feedback: {
    // 提交反馈
    submitFeedback: (data) => callCloudFunction('feedback-submit', data)
  },

  // 浏览历史相关
  history: {
    // 获取浏览历史列表
    getHistoryList: (params) => callCloudFunction('history-getList', params),
    
    // 添加浏览历史
    addHistory: (data) => callCloudFunction('history-add', data),
    
    // 删除浏览历史
    removeHistory: (data) => callCloudFunction('history-remove', data),
    
    // 清空所有浏览历史
    clearAllHistory: (params) => callCloudFunction('history-remove', { ...params, action: 'clearAll' })
  },
  
  // 数据库直接操作方法
  db: {
    // 获取用户收藏列表
    getUserFavorites: (userId) => {
      const { db } = getDB()
      return db.collection('favorites')
        .where({
          userId: userId
        })
        .get()
    },
    
    // 获取用户浏览历史
    getUserHistory: (userId, limit = 10) => {
      const { db } = getDB()
      return db.collection('history')
        .where({
          userId: userId
        })
        .orderBy('viewTime', 'desc')
        .limit(limit)
        .get()
    },
    
    // 添加浏览历史
    addHistory: (data) => {
      const { db } = getDB()
      return db.collection('history').add({
        data
      })
    },
    
    // 获取热门资料
    getHotMaterials: (limit = 10) => {
      const { db } = getDB()
      return db.collection('materials')
        .orderBy('downloadCount', 'desc')
        .limit(limit)
        .get()
    }
  }
}

// 导出
module.exports = {
  api,
  callCloudFunction,
  getDB
}
