<template>
  <div>
    <div style="margin-bottom: 20px;">
      <el-button type="primary" @click="handleAdd">添加分类</el-button>
      <el-button @click="initCategories" :loading="initializing">初始化默认分类</el-button>
    </div>

    <!-- 分类类型标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="年级分类" name="grade">
        <CategoryTable 
          :data="gradeCategories" 
          :loading="loading"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </el-tab-pane>
      <el-tab-pane label="科目分类" name="subject">
        <CategoryTable 
          :data="subjectCategories" 
          :loading="loading"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </el-tab-pane>
      <el-tab-pane label="册别分类" name="volume">
        <CategoryTable 
          :data="volumeCategories" 
          :loading="loading"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </el-tab-pane>
      <el-tab-pane label="板块分类" name="section">
        <CategoryTable 
          :data="sectionCategories" 
          :loading="loading"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑分类' : '添加分类'" 
      width="500px"
      @close="resetForm"
    >
      <el-form :model="form" label-width="80px" :disabled="submitting">
        <el-form-item label="分类名称" required>
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类类型" required>
          <el-select v-model="form.type" placeholder="请选择分类类型" :disabled="isEdit">
            <el-option label="年级分类" value="grade" />
            <el-option label="科目分类" value="subject" />
            <el-option label="册别分类" value="volume" />
            <el-option label="板块分类" value="section" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序" required>
          <el-input-number v-model="form.order" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="状态" required>
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="submitting">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ submitting ? '提交中...' : '确认' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCategoryList, createCategory, updateCategory, deleteCategory, initDefaultCategories, type Category } from '../api/category'
import CategoryTable from '../components/CategoryTable.vue'

const activeTab = ref('grade')
const dialogVisible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const initializing = ref(false)
const isEdit = ref(false)
const currentEditId = ref('')

const categories = ref<Category[]>([])

const form = reactive({
  name: '',
  type: 'grade' as Category['type'],
  order: 1,
  status: 'active' as Category['status']
})

// 按类型分组的分类数据
const gradeCategories = computed(() => categories.value.filter(cat => cat.type === 'grade'))
const subjectCategories = computed(() => categories.value.filter(cat => cat.type === 'subject'))
const volumeCategories = computed(() => categories.value.filter(cat => cat.type === 'volume'))
const sectionCategories = computed(() => categories.value.filter(cat => cat.type === 'section'))

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const response = await getCategoryList()
    if (response.success) {
      categories.value = response.data
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  form.type = tabName as Category['type']
}

// 添加分类
const handleAdd = () => {
  isEdit.value = false
  form.type = activeTab.value as Category['type']
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (category: Category) => {
  isEdit.value = true
  currentEditId.value = category._id!
  form.name = category.name
  form.type = category.type
  form.order = category.order
  form.status = category.status
  dialogVisible.value = true
}

// 删除分类
const handleDelete = async (category: Category) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await deleteCategory(category._id!)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchCategories()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!form.name.trim()) {
    ElMessage.warning('请输入分类名称')
    return
  }

  submitting.value = true
  try {
    let response
    if (isEdit.value) {
      response = await updateCategory(currentEditId.value, {
        name: form.name,
        order: form.order,
        status: form.status
      })
    } else {
      response = await createCategory({
        name: form.name,
        type: form.type,
        order: form.order,
        status: form.status
      })
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      resetForm()
      fetchCategories()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.name = ''
  form.type = 'grade'
  form.order = 1
  form.status = 'active'
  currentEditId.value = ''
}

// 初始化默认分类
const initCategories = async () => {
  initializing.value = true
  try {
    const response = await initDefaultCategories()
    if (response.success) {
      ElMessage.success(response.message)
      fetchCategories()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('初始化分类失败:', error)
    ElMessage.error('初始化分类失败')
  } finally {
    initializing.value = false
  }
}

onMounted(() => {
  fetchCategories()
})
</script>