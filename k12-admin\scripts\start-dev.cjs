const { exec, spawn } = require('child_process');
const os = require('os');

const TARGET_PORT = 5173;

// 获取操作系统类型
const isWindows = os.platform() === 'win32';

/**
 * 杀死占用指定端口的进程
 */
function killProcessOnPort(port) {
  return new Promise((resolve) => {
    let command;
    
    if (isWindows) {
      // Windows系统使用netstat和taskkill
      command = `netstat -ano | findstr :${port}`;
    } else {
      // Unix/Linux/Mac系统使用lsof
      command = `lsof -ti:${port}`;
    }
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.log(`端口 ${port} 未被占用或查询失败`);
        resolve();
        return;
      }
      
      if (!stdout.trim()) {
        console.log(`端口 ${port} 未被占用`);
        resolve();
        return;
      }
      
      if (isWindows) {
        // Windows: 从netstat输出中提取PID
        const lines = stdout.trim().split('\n');
        const pids = [];
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5) {
            const localAddress = parts[1];
            const pid = parts[4];
            
            // 检查是否是目标端口
            if (localAddress.includes(`:${port}`)) {
              pids.push(pid);
            }
          }
        });
        
        if (pids.length === 0) {
          console.log(`端口 ${port} 未被占用`);
          resolve();
          return;
        }
        
        // 杀死所有相关进程
        const killPromises = pids.map(pid => {
          return new Promise((killResolve) => {
            exec(`taskkill /F /PID ${pid}`, (killError) => {
              if (killError) {
                console.log(`杀死进程 ${pid} 失败:`, killError.message);
              } else {
                console.log(`已杀死占用端口 ${port} 的进程 ${pid}`);
              }
              killResolve();
            });
          });
        });
        
        Promise.all(killPromises).then(() => {
          // 等待一下确保进程完全结束
          setTimeout(resolve, 1000);
        });
        
      } else {
        // Unix/Linux/Mac: lsof直接返回PID列表
        const pids = stdout.trim().split('\n').filter(pid => pid);
        
        if (pids.length === 0) {
          console.log(`端口 ${port} 未被占用`);
          resolve();
          return;
        }
        
        // 杀死所有相关进程
        const killCommand = `kill -9 ${pids.join(' ')}`;
        exec(killCommand, (killError) => {
          if (killError) {
            console.log(`杀死进程失败:`, killError.message);
          } else {
            console.log(`已杀死占用端口 ${port} 的进程: ${pids.join(', ')}`);
          }
          // 等待一下确保进程完全结束
          setTimeout(resolve, 1000);
        });
      }
    });
  });
}

/**
 * 启动开发服务器
 */
function startDevServer() {
  console.log(`正在启动开发服务器，端口: ${TARGET_PORT}`);
  
  // 使用npm run dev启动
  const child = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  });
  
  child.on('error', (error) => {
    console.error('启动开发服务器失败:', error);
    process.exit(1);
  });
  
  child.on('close', (code) => {
    console.log(`开发服务器进程退出，退出码: ${code}`);
    process.exit(code);
  });
  
  // 处理进程退出信号
  process.on('SIGINT', () => {
    console.log('\n正在关闭开发服务器...');
    child.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('\n正在关闭开发服务器...');
    child.kill('SIGTERM');
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('=== K12管理系统开发服务器启动脚本 ===');
  console.log(`目标端口: ${TARGET_PORT}`);
  console.log(`操作系统: ${os.platform()}`);
  
  try {
    // 1. 杀死占用目标端口的进程
    console.log('\n1. 检查并清理端口占用...');
    await killProcessOnPort(TARGET_PORT);
    
    // 2. 启动开发服务器
    console.log('\n2. 启动开发服务器...');
    startDevServer();
    
  } catch (error) {
    console.error('启动失败:', error);
    process.exit(1);
  }
}

// 运行主函数
main();