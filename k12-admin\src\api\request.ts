import { ElMessage } from 'element-plus';
import { getFileList, getUploadUrl, createFileRecord } from './cloudbase';

/**
 * 统一的API调用函数，现在直接使用云开发SDK
 * @param {string} action 要调用的操作名
 * @param {object} payload 传递给操作的参数
 * @returns Promise
 */
export default async function request({ action, payload }: { action: string, payload: any }) {
  try {
    let result;
    
    switch (action) {
      case 'getFileList':
        result = await getFileList(
          payload?.query || '{}',
          payload?.limit || 10,
          payload?.offset || 0
        );
        break;
      case 'getUploadUrl':
        result = await getUploadUrl((payload as any)?.cloudPath);
        break;
      case 'createFileRecord':
        result = await createFileRecord((payload as any)?.file);
        break;
      default:
        result = {
          code: 404,
          message: `未找到操作: ${action}`
        };
    }
    
    // 如果操作失败，显示错误消息
    if (result.code !== 200) {
      ElMessage.error(result.message || '操作失败');
    }
    
    return result;
  } catch (error: any) {
    console.error('API调用错误:', error);
    ElMessage.error(error?.message || '网络请求失败，请稍后重试');
    return Promise.reject(error);
  }
}
