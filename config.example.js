// 云开发配置文件示例
// 复制此文件为 config.js 并填入真实的配置信息

module.exports = {
  // 云环境配置
  ENV_ID: 'cloud1-8gm001v7fd56ff43',
  APP_ID: 'wxdcb01784f343322b',
  
  // 腾讯云API密钥 (可选，用于服务端SDK)
  // 获取方式: https://console.cloud.tencent.com/cam/capi
  SECRET_ID: 'your-secret-id-here',
  SECRET_KEY: 'your-secret-key-here',
  
  // 数据库集合配置
  COLLECTIONS: {
    users: 'users',
    files: 'files',
    point_records: 'point_records',
    favorites: 'favorites',
    downloads: 'downloads',
    shares: 'shares',
    test_logs: 'test_logs'
  },
  
  // 测试配置
  TEST_CONFIG: {
    test_openid: 'test_user_123456',
    batch_size: 100,
    max_records: 1000
  }
};
