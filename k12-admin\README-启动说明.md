# K12管理系统 - 启动说明

## 自动端口管理功能

本项目已配置自动端口管理功能，确保开发服务器始终在 **5173端口** 启动。

### 功能特性

- ✅ 自动检测并杀死占用5173端口的进程
- ✅ 强制在5173端口启动开发服务器
- ✅ 跨平台支持（Windows/Mac/Linux）
- ✅ 自动打开浏览器
- ✅ 支持外部访问

## 启动方式

### 方式1: 使用npm命令（推荐）

```bash
# 普通启动（如果端口被占用会报错）
npm run dev

# 强制启动（自动杀死占用端口的进程）
npm run start
# 或
npm run dev:force
```

### 方式2: 双击批处理文件（Windows用户）

直接双击 `启动开发服务器.bat` 文件

### 方式3: 使用PowerShell脚本（Windows用户）

右键点击 `启动开发服务器.ps1` → 选择"使用PowerShell运行"

## 端口配置

- **开发端口**: 5173（固定）
- **strictPort**: true（端口被占用时不会自动切换）
- **host**: true（允许外部访问）
- **open**: true（自动打开浏览器）

## 故障排除

### 如果启动失败

1. **检查Node.js环境**
   ```bash
   node --version
   npm --version
   ```

2. **手动清理端口**
   ```bash
   # Windows
   netstat -ano | findstr :5173
   taskkill /F /PID <进程ID>
   
   # Mac/Linux
   lsof -ti:5173
   kill -9 <进程ID>
   ```

3. **重新安装依赖**
   ```bash
   npm install
   ```

### 常见问题

**Q: 为什么要固定在5173端口？**
A: 因为云开发的安全域名配置中指定了5173端口，更换端口会导致跨域问题。

**Q: 如果我想使用其他端口怎么办？**
A: 需要同时修改：
- `vite.config.ts` 中的端口配置
- `scripts/start-dev.js` 中的 `TARGET_PORT` 变量
- 云开发控制台的安全域名配置

**Q: 脚本执行权限问题（PowerShell）**
A: 以管理员身份运行PowerShell，执行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 开发服务器信息

启动成功后，开发服务器将在以下地址可用：
- 本地访问: http://localhost:5173
- 网络访问: http://[你的IP]:5173

浏览器会自动打开并导航到管理系统首页。