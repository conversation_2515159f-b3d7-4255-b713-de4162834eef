# K12管理系统 - PowerShell启动脚本
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "K12管理系统 - 开发服务器"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    K12管理系统 - 开发服务器启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 切换到脚本所在目录
Set-Location $PSScriptRoot

# 检查Node.js环境
Write-Host "正在检查Node.js环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查npm环境
Write-Host "正在检查npm环境..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到npm，请检查Node.js安装" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "正在启动开发服务器..." -ForegroundColor Green
Write-Host "目标端口: 5173" -ForegroundColor Cyan
Write-Host "如果端口被占用，将自动杀死占用进程" -ForegroundColor Yellow
Write-Host ""

# 启动开发服务器
try {
    npm run start
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "开发服务器已关闭" -ForegroundColor Yellow
    Read-Host "按任意键退出"
}