// 云环境数据库连接测试脚本
const tcb = require('tcb-admin-node');

// 云环境配置
const ENV_ID = 'cloud1-8gm001v7fd56ff43';
const APP_ID = 'wxdcb01784f343322b';

// 初始化云开发
const app = tcb.init({
  env: ENV_ID,
  // 如果需要使用服务端SDK，需要配置secretId和secretKey
  // secretId: 'your-secret-id',
  // secretKey: 'your-secret-key'
});

const db = app.database();

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('开始测试云环境数据库连接...');
  console.log(`环境ID: ${ENV_ID}`);
  console.log(`小程序ID: ${APP_ID}`);
  console.log('='.repeat(50));

  try {
    // 测试1: 获取数据库集合列表
    console.log('\n1. 测试获取数据库集合信息...');
    
    // 测试users集合
    const usersCollection = db.collection('users');
    const usersCount = await usersCollection.count();
    console.log(`✓ users集合连接成功，当前记录数: ${usersCount.total}`);

    // 测试files集合
    const filesCollection = db.collection('files');
    const filesCount = await filesCollection.count();
    console.log(`✓ files集合连接成功，当前记录数: ${filesCount.total}`);

    // 测试point_records集合
    const pointRecordsCollection = db.collection('point_records');
    const pointRecordsCount = await pointRecordsCollection.count();
    console.log(`✓ point_records集合连接成功，当前记录数: ${pointRecordsCount.total}`);

    // 测试favorites集合
    const favoritesCollection = db.collection('favorites');
    const favoritesCount = await favoritesCollection.count();
    console.log(`✓ favorites集合连接成功，当前记录数: ${favoritesCount.total}`);

    // 测试downloads集合
    const downloadsCollection = db.collection('downloads');
    const downloadsCount = await downloadsCollection.count();
    console.log(`✓ downloads集合连接成功，当前记录数: ${downloadsCount.total}`);

    // 测试shares集合
    const sharesCollection = db.collection('shares');
    const sharesCount = await sharesCollection.count();
    console.log(`✓ shares集合连接成功，当前记录数: ${sharesCount.total}`);

    console.log('\n2. 测试数据查询操作...');
    
    // 查询用户数据示例
    const userQuery = await usersCollection.limit(3).get();
    console.log(`✓ 查询用户数据成功，获取到 ${userQuery.data.length} 条记录`);
    if (userQuery.data.length > 0) {
      console.log('   示例用户数据:');
      userQuery.data.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.nickname || '未知用户'} (${user.openid}) - 积分: ${user.points || 0}`);
      });
    }

    // 查询文件数据示例
    const fileQuery = await filesCollection.limit(3).get();
    console.log(`✓ 查询文件数据成功，获取到 ${fileQuery.data.length} 条记录`);
    if (fileQuery.data.length > 0) {
      console.log('   示例文件数据:');
      fileQuery.data.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file.title || '未知文件'} - 类型: ${file.type || '未知'} - 下载次数: ${file.download_count || 0}`);
      });
    }

    console.log('\n3. 测试数据写入操作...');
    
    // 创建测试记录
    const testRecord = {
      test_id: 'tcp_connection_test_' + Date.now(),
      test_time: new Date(),
      test_type: 'tcp_connection',
      test_result: 'success',
      environment: ENV_ID,
      app_id: APP_ID
    };

    // 尝试写入测试数据到test_logs集合
    const testLogsCollection = db.collection('test_logs');
    const addResult = await testLogsCollection.add(testRecord);
    console.log(`✓ 写入测试数据成功，记录ID: ${addResult.id}`);

    // 验证写入的数据
    const verifyQuery = await testLogsCollection.doc(addResult.id).get();
    if (verifyQuery.data) {
      console.log(`✓ 验证写入数据成功，测试ID: ${verifyQuery.data.test_id}`);
    }

    console.log('\n4. 测试数据删除操作...');
    
    // 删除刚才创建的测试记录
    await testLogsCollection.doc(addResult.id).remove();
    console.log(`✓ 删除测试数据成功`);

    // 验证删除结果
    const deleteVerifyQuery = await testLogsCollection.doc(addResult.id).get();
    if (!deleteVerifyQuery.data) {
      console.log(`✓ 验证删除操作成功，记录已不存在`);
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 数据库连接测试完成！所有操作均成功！');
    console.log('✓ TCP连接正常');
    console.log('✓ 读取操作正常');
    console.log('✓ 写入操作正常');
    console.log('✓ 删除操作正常');
    console.log('✓ 云环境数据库可以正常使用');

  } catch (error) {
    console.error('\n❌ 数据库连接测试失败:');
    console.error('错误信息:', error.message);
    console.error('错误详情:', error);
    
    // 提供解决建议
    console.log('\n💡 可能的解决方案:');
    console.log('1. 检查云环境ID是否正确');
    console.log('2. 检查小程序ID是否正确');
    console.log('3. 确认云开发服务已开通');
    console.log('4. 检查网络连接是否正常');
    console.log('5. 如果是权限问题，可能需要配置secretId和secretKey');
  }
}

// 测试云函数调用
async function testCloudFunction() {
  console.log('\n' + '='.repeat(50));
  console.log('开始测试云函数调用...');

  try {
    // 测试调用login云函数
    console.log('\n1. 测试login云函数...');
    const loginResult = await app.callFunction({
      name: 'login',
      data: {}
    });
    
    if (loginResult.result) {
      console.log('✓ login云函数调用成功');
      console.log(`   返回数据:`, loginResult.result);
    }

    // 测试调用user云函数
    console.log('\n2. 测试user云函数...');
    const userResult = await app.callFunction({
      name: 'user',
      data: {
        action: 'getProfile',
        data: { openid: 'test_user_123456' }
      }
    });
    
    if (userResult.result) {
      console.log('✓ user云函数调用成功');
      console.log(`   返回数据:`, userResult.result);
    }

    console.log('\n🎉 云函数测试完成！');

  } catch (error) {
    console.error('\n❌ 云函数测试失败:');
    console.error('错误信息:', error.message);
    console.error('错误详情:', error);
  }
}

// 主函数
async function main() {
  console.log('🚀 开始云环境连接测试');
  console.log('时间:', new Date().toLocaleString());
  
  // 测试数据库连接
  await testDatabaseConnection();
  
  // 测试云函数调用
  await testCloudFunction();
  
  console.log('\n📋 测试总结:');
  console.log('- 数据库TCP连接测试完成');
  console.log('- 云函数调用测试完成');
  console.log('- 可以开始进行后续的云函数开发和数据操作');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testDatabaseConnection,
  testCloudFunction,
  db,
  app
};
