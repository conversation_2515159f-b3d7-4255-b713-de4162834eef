// 添加热门搜索配置的脚本
const tcb = require('@cloudbase/node-sdk');

const app = tcb.init({
  env: 'cloud1-8gm001v7fd56ff43'
});

const db = app.database();

async function addHotSearchConfig() {
  try {
    // 添加热门搜索关键词配置
    const result = await db.collection('system_configs').add({
      key: 'hot_search_keywords',
      value: '期末试卷,单元测试,练习册,知识点总结,作文素材,数学练习',
      type: 'string',
      category: 'ui',
      description: '热门搜索关键词，逗号分隔',
      is_editable: true,
      created_time: new Date(),
      updated_time: new Date()
    });
    
    console.log('热门搜索配置添加成功:', result);
    return result;
  } catch (error) {
    console.error('添加配置失败:', error);
    throw error;
  }
}

// 执行添加配置
addHotSearchConfig()
  .then(() => {
    console.log('配置添加完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('执行失败:', error);
    process.exit(1);
  });