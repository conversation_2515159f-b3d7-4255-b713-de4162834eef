import { db } from './cloudbase'

// 系统配置服务类
export class ConfigService {
  private static instance: ConfigService
  private configCollection = db.collection('system_config')

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService()
    }
    return ConfigService.instance
  }

  // 保存基础配置
  async saveBasicConfig(config: any) {
    try {
      const result = await this.configCollection
        .where({ type: 'basic' })
        .update({
          data: {
            ...config,
            updatedAt: new Date()
          }
        })
      
      if (result.stats.updated === 0) {
        // 如果没有更新记录，则创建新记录
        await this.configCollection.add({
          data: {
            type: 'basic',
            ...config,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('保存基础配置失败:', error)
      return { success: false, error }
    }
  }

  // 保存功能开关配置
  async saveFeaturesConfig(config: any) {
    try {
      const result = await this.configCollection
        .where({ type: 'features' })
        .update({
          data: {
            ...config,
            updatedAt: new Date()
          }
        })
      
      if (result.stats.updated === 0) {
        await this.configCollection.add({
          data: {
            type: 'features',
            ...config,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('保存功能开关配置失败:', error)
      return { success: false, error }
    }
  }

  // 保存业务参数配置
  async saveBusinessConfig(config: any) {
    try {
      const result = await this.configCollection
        .where({ type: 'business' })
        .update({
          data: {
            ...config,
            updatedAt: new Date()
          }
        })
      
      if (result.stats.updated === 0) {
        await this.configCollection.add({
          data: {
            type: 'business',
            ...config,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('保存业务参数配置失败:', error)
      return { success: false, error }
    }
  }

  // 保存存储配置
  async saveStorageConfig(config: any) {
    try {
      const result = await this.configCollection
        .where({ type: 'storage' })
        .update({
          data: {
            ...config,
            updatedAt: new Date()
          }
        })
      
      if (result.stats.updated === 0) {
        await this.configCollection.add({
          data: {
            type: 'storage',
            ...config,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('保存存储配置失败:', error)
      return { success: false, error }
    }
  }

  // 保存开发环境配置
  async saveSecurityConfig(config: any) {
    try {
      const result = await this.configCollection
        .where({ type: 'security' })
        .update({
          data: {
            ...config,
            updatedAt: new Date()
          }
        })
      
      if (result.stats.updated === 0) {
        await this.configCollection.add({
          data: {
            type: 'security',
            ...config,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
      }
      
      return { success: true }
    } catch (error) {
      console.error('保存开发环境配置失败:', error)
      return { success: false, error }
    }
  }

  // 获取配置
  async getConfig(type: string) {
    try {
      const result = await this.configCollection
        .where({ type })
        .get()
      
      if (result.data.length > 0) {
        return { success: true, data: result.data[0] }
      } else {
        return { success: true, data: null }
      }
    } catch (error) {
      console.error(`获取${type}配置失败:`, error)
      return { success: false, error }
    }
  }

  // 获取所有配置
  async getAllConfigs() {
    try {
      const result = await this.configCollection.get()
      const configs: any = {}
      
      result.data.forEach((item: any) => {
        configs[item.type] = item
      })
      
      return { success: true, data: configs }
    } catch (error) {
      console.error('获取所有配置失败:', error)
      return { success: false, error }
    }
  }

  // 重置配置到默认值
  async resetConfig(type: string) {
    try {
      await this.configCollection
        .where({ type })
        .remove()
      
      return { success: true }
    } catch (error) {
      console.error(`重置${type}配置失败:`, error)
      return { success: false, error }
    }
  }
}

// 导出单例实例
export const configService = ConfigService.getInstance()