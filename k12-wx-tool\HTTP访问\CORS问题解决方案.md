# CORS跨域问题解决方案

## 问题描述
后台管理系统访问云函数API时出现CORS跨域错误：
```
Access to XMLHttpRequest at 'https://cloud1-8gm001v7fd56ff43.service.tcloudbase.com/admin-api' from origin 'http://localhost:5173' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

### 方案一：云函数CORS配置（未成功）
尝试在云函数中添加CORS头部，但由于云函数执行环境问题未能成功：

```javascript
// 在云函数中添加CORS头部
exports.main = async (event, context) => {
  // 处理预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
        'Access-Control-Max-Age': '86400'
      },
      body: ''
    };
  }
  
  // 在响应中添加CORS头部
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(responseData),
  };
};
```

### 方案二：Vite代理配置（临时方案）
在前端开发环境中配置代理来避免CORS问题：

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/admin-api': {
        target: 'https://cloud1-8gm001v7fd56ff43.service.tcloudbase.com',
        changeOrigin: true,
        secure: true,
      }
    }
  }
})
```

### 方案三：直接使用云开发SDK（最终解决方案）✅

**重要说明：云开发SDK是安装在前端项目中，直接在浏览器中运行，不是在云函数中使用！**

#### 1. 在前端项目中安装云开发SDK
```bash
# 在前端项目目录（k12-admin）中执行
npm install @cloudbase/js-sdk
```

#### 2. 创建云开发API封装
```typescript
// src/api/cloudbase.ts
import cloudbase from '@cloudbase/js-sdk';

// 初始化云开发 - 注意：无需 SecretId/SecretKey！
const app = cloudbase.init({
  env: 'cloud1-8gm001v7fd56ff43'
  // 云开发SDK会自动处理身份验证，无需手动配置密钥
});

// 获取数据库引用
const db = app.database();
const storage = app.storage();

// 封装API方法
export async function getFileList() {
  const result = await db.collection('files')
    .orderBy('createTime', 'desc')
    .get();
  
  return {
    code: 200,
    message: '获取列表成功',
    data: result.data
  };
}

export async function getUploadUrl(cloudPath: string) {
  const result = await storage.getUploadMetadata({
    cloudPath: cloudPath
  });

  return {
    code: 200,
    message: '获取成功',
    data: result
  };
}

export async function createFileRecord(file: any) {
  const result = await db.collection('files').add({
    ...file,
    createTime: db.serverDate()
  });

  return {
    code: 200,
    message: '创建成功',
    data: result
  };
}
```

#### 3. 修改请求封装
```typescript
// src/api/request.ts
import { getFileList, getUploadUrl, createFileRecord } from './cloudbase';

export default async function request({ action, payload }: { action: string, payload: object }) {
  let result;
  
  switch (action) {
    case 'getFileList':
      result = await getFileList();
      break;
    case 'getUploadUrl':
      result = await getUploadUrl(payload.cloudPath as string);
      break;
    case 'createFileRecord':
      result = await createFileRecord(payload.file);
      break;
    default:
      result = {
        code: 404,
        message: `未找到操作: ${action}`
      };
  }
  
  return result;
}
```

## 重要概念澄清

### 云开发SDK的使用位置
- **安装位置**：前端项目（k12-admin目录）
- **运行环境**：浏览器
- **作用**：直接在前端连接云开发数据库和存储服务
- **认证方式**：**无需 SecretId/SecretKey**，通过安全域名和安全规则控制访问
- **优势**：绕过HTTP请求，避免CORS跨域问题

### 重要：无需配置密钥！

云开发 SDK 与传统云服务 API 的重要区别：

**传统云服务 API（不适合前端）**：
```javascript
// 需要配置密钥（不安全，不能在前端使用）
const client = new CloudAPI({
  secretId: 'your-secret-id',     // ❌ 前端不能暴露
  secretKey: 'your-secret-key'    // ❌ 前端不能暴露
});
```

**云开发 SDK（专为前端设计）**：
```javascript
// 无需密钥配置，安全可靠
const app = cloudbase.init({
  env: 'cloud1-8gm001v7fd56ff43'  // ✅ 只需环境ID
});
```

### 云开发的安全机制

云开发通过以下方式保证安全性，无需在前端暴露密钥：

1. **安全域名控制**
   - 在云开发控制台配置允许访问的域名
   - 只有来自指定域名的请求才能访问云开发服务
   - 开发环境需要添加 `localhost:5173` 到安全域名列表

2. **数据库安全规则**
   - 通过安全规则控制数据的读写权限
   - 可以基于用户身份、数据内容等条件设置规则
   - 示例规则：`auth != null` （只允许已登录用户访问）

3. **用户身份验证**
   - 支持匿名登录、邮箱登录、微信登录等多种方式
   - SDK 会自动管理用户的登录状态和权限

### 配置步骤

1. **添加安全域名**（必需）
   ```
   在云开发控制台 → 环境设置 → 安全配置 → 安全域名
   添加：http://localhost:5173
   ```

2. **配置数据库安全规则**（可选，根据需要）
   ```javascript
   // 示例：允许所有人读取，只允许登录用户写入
   {
     "read": true,
     "write": "auth != null"
   }
   ```

3. **用户登录**（如果需要身份验证）
   ```javascript
   // 匿名登录示例
   await app.auth().signInAnonymously();
   ```

### 架构对比

#### 原来的架构（有CORS问题）
```
前端(localhost:5173) → HTTP请求 → 云函数API → 云开发数据库
                    ↑ CORS跨域问题出现在这里
```

#### 新架构（无CORS问题）
```
前端(localhost:5173) → 云开发SDK → 直接连接云开发数据库
                    ↑ 不经过HTTP请求，无CORS问题
```

## 解决方案优势

### 方案三的优势（推荐）
1. **完全避免CORS问题** - 直接使用云开发SDK，不通过HTTP请求
2. **更好的性能** - 减少网络请求中间层
3. **更简单的维护** - 不需要维护云函数HTTP触发器
4. **更好的错误处理** - 直接在前端处理数据库操作错误
5. **开发体验更好** - 不需要配置复杂的CORS规则

### 其他方案的局限性
- **方案一**：需要云函数HTTP触发器正确配置，且云函数执行环境可能有问题
- **方案二**：只在开发环境有效，生产环境仍需解决CORS问题

## 最佳实践建议

1. **优先使用云开发SDK** - 对于云开发项目，直接使用SDK是最佳选择
2. **云函数适用场景** - 复杂业务逻辑、需要服务端处理的场景
3. **CORS配置** - 如果必须使用HTTP API，确保在云开发控制台正确配置CORS
4. **安全考虑** - 生产环境中不要使用 `*` 作为允许的源，应指定具体域名

## 云函数清理

由于采用了直接使用云开发SDK的方案，原本的 `admin-api` 云函数已不再需要：

### 已完成的清理工作
- ✅ **删除本地文件**：已删除 `k12-wx/cloudfunctions/admin-api/` 目录及其所有文件

### 需要手动完成的清理工作
- ⚠️ **删除云端函数**：需要在云开发控制台手动删除 `admin-api` 云函数
  - 访问：[云函数管理页面](https://tcb.cloud.tencent.com/dev?envId=cloud1-8gm001v7fd56ff43#/scf/detail?id=admin-api&NameSpace=cloud1-8gm001v7fd56ff43)
  - 操作：找到 `admin-api` 函数，点击删除按钮

### 清理的好处
1. **降低维护成本** - 无需维护不必要的云函数代码
2. **减少资源消耗** - 避免云函数的计算资源占用
3. **简化架构** - 架构更加清晰简单
4. **降低出错概率** - 减少了一个可能出错的环节

## 相关文档
- [云开发JavaScript SDK文档](https://docs.cloudbase.net/api-reference/webv2/initialization.html)
- [腾讯云开发控制台](https://console.cloud.tencent.com/tcb)
- [Vite代理配置文档](https://vitejs.dev/config/server-options.html#server-proxy)
- [云函数管理控制台](https://console.cloud.tencent.com/tcb/scf)
