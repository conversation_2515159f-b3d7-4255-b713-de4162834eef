// 获取资料筛选选项云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('开始获取筛选选项...')
    
    // 先获取所有活跃的文件记录，排除升学专区
    const filesResult = await db.collection('files')
      .where({
        status: 'active',
        grade: db.command.neq('升学专区')  // 排除升学专区
      })
      .field({
        grade: true,
        subject: true,
        volume: true,
        section: true
      })
      .get()
    
    console.log('获取到文件数量:', filesResult.data.length)
    
    if (!filesResult.data || filesResult.data.length === 0) {
      return {
        success: true,
        data: {
          grades: [],
          subjects: [],
          volumes: [],
          sections: []
        },
        message: '暂无数据'
      }
    }
    
    // 统计各个字段的选项和数量
    const gradeMap = new Map()
    const subjectMap = new Map()
    const volumeMap = new Map()
    const sectionMap = new Map()
    
    filesResult.data.forEach(file => {
      // 统计年级（排除升学专区）
      if (file.grade && file.grade !== '升学专区') {
        gradeMap.set(file.grade, (gradeMap.get(file.grade) || 0) + 1)
      }
      
      // 统计学科
      if (file.subject) {
        subjectMap.set(file.subject, (subjectMap.get(file.subject) || 0) + 1)
      }
      
      // 统计学期（排除"专区"）
      if (file.volume && file.volume !== '专区') {
        volumeMap.set(file.volume, (volumeMap.get(file.volume) || 0) + 1)
      }
      
      // 统计类别
      if (file.section) {
        sectionMap.set(file.section, (sectionMap.get(file.section) || 0) + 1)
      }
    })
    
    // 转换为数组格式，并排序
    const formatOptions = (map, type) => {
      const items = Array.from(map.entries())
        .map(([name, count]) => ({
          type: type,
          name: name,
          count: count,
          is_active: true
        }))
      
      // 特殊处理年级排序，排除升学专区
      if (type === 'grade') {
        const gradeOrder = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
        return items
          .filter(item => gradeOrder.includes(item.name)) // 只保留一到六年级
          .sort((a, b) => {
            const indexA = gradeOrder.indexOf(a.name)
            const indexB = gradeOrder.indexOf(b.name)
            return indexA - indexB
          })
          .map((item, index) => ({
            ...item,
            sort_order: index + 1
          }))
      }
      
      // 特殊处理科目排序，语文、数学、英语优先
      if (type === 'subject') {
        const subjectOrder = ['语文', '数学', '英语']
        const prioritySubjects = []
        const otherSubjects = []
        
        items.forEach(item => {
          if (subjectOrder.includes(item.name)) {
            prioritySubjects.push(item)
          } else {
            otherSubjects.push(item)
          }
        })
        
        // 优先科目按指定顺序排序
        prioritySubjects.sort((a, b) => {
          const indexA = subjectOrder.indexOf(a.name)
          const indexB = subjectOrder.indexOf(b.name)
          return indexA - indexB
        })
        
        // 其他科目按中文排序
        otherSubjects.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
        
        // 合并并重新设置排序号
        const sortedSubjects = [...prioritySubjects, ...otherSubjects]
        return sortedSubjects.map((item, index) => ({
          ...item,
          sort_order: index + 1
        }))
      }
      
      // 其他类型按中文排序
      return items
        .sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
        .map((item, index) => ({
          ...item,
          sort_order: index + 1
        }))
    }
    
    const grades = formatOptions(gradeMap, 'grade')
    const subjects = formatOptions(subjectMap, 'subject')
    const volumes = formatOptions(volumeMap, 'volume')
    const sections = formatOptions(sectionMap, 'section')
    
    console.log('筛选选项统计完成:', {
      grades: grades.length,
      subjects: subjects.length,
      volumes: volumes.length,
      sections: sections.length,
      total: grades.length + subjects.length + volumes.length + sections.length
    })
    
    // 返回前端需要的数据格式
    const result = {
      success: true,
      data: {
        // 返回简单的字符串数组，供前端使用
        grades: grades.map(item => item.name),
        subjects: subjects.map(item => item.name),
        volumes: volumes.map(item => item.name),
        sections: sections.map(item => item.name),
        // 同时保留详细信息，供后续扩展使用
        gradeDetails: grades,
        subjectDetails: subjects,
        volumeDetails: volumes,
        sectionDetails: sections
      },
      message: '获取筛选选项成功'
    }
    
    console.log('返回筛选选项结果:', JSON.stringify(result, null, 2))
    
    return result
    
  } catch (error) {
    console.error('获取筛选选项失败:', error)
    return {
      success: false,
      data: {
        grades: [],
        subjects: [],
        volumes: [],
        sections: []
      },
      message: '获取筛选选项失败: ' + error.message
    }
  }
}