// 资料详情页逻辑
const app = getApp()

Page({
  data: {
    // 资料信息
    materialInfo: {},
    
    // 相关推荐
    relatedMaterials: [],
    
    // 加载状态
    loading: false,
    
    // 资料ID
    materialId: null
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.setData({ materialId: id })
      this.loadMaterialDetail(id)
      this.loadRelatedMaterials(id)
    }
  },

  onShow() {
    // 页面显示时刷新收藏状态
    if (this.data.materialId) {
      this.checkFavoriteStatus()
    }
  },

  // 加载资料详情
  async loadMaterialDetail(id) {
    this.setData({ loading: true })
    
    try {
      console.log('正在加载资料详情，ID:', id)
      
      const result = await app.api.material.getMaterialDetail(id)
      console.log('资料详情API返回结果:', result)
      
      if (result.success && result.data) {
        // 处理数据格式，确保兼容性
        const materialData = {
          id: result.data._id || id,
          title: result.data.title || '未知资料',
          cover: result.data.cover || '/images/default_cover.jpg',
          tags: result.data.tags || [result.data.grade, result.data.subject, result.data.section].filter(Boolean),
          downloadCount: result.data.downloadCount || result.data.download_count || 0,
          viewCount: result.data.viewCount || result.data.view_count || 0,
          rating: result.data.rating || 4.5,
          points: result.data.points || 0,
          description: result.data.description || '暂无描述',
          previewImages: result.data.previewImages || [],
          isFavorited: result.data.isFavorited || false,
          // 添加更多字段
          grade: result.data.grade,
          subject: result.data.subject,
          section: result.data.section,
          volume: result.data.volume,
          type: result.data.type,
          fileSize: result.data.size || result.data.fileSize
        }
        
        this.setData({
          materialInfo: materialData
        })
        
        // 更新页面标题
        wx.setNavigationBarTitle({
          title: materialData.title
        })
        
        // 添加浏览历史
        this.addToHistory(materialData)
        
      } else {
        console.error('获取资料详情失败:', result.message)
        wx.showToast({
          title: result.message || '资料不存在',
          icon: 'none'
        })
        
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载资料详情失败:', error)
      
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载相关推荐
  async loadRelatedMaterials(id) {
    try {
      const result = await app.api.material.getRecommendMaterials(id)
      
      if (result.success) {
        this.setData({
          relatedMaterials: result.data.list || []
        })
      } else {
        this.setData({
          relatedMaterials: []
        })
      }
    } catch (error) {
      console.error('加载相关推荐失败:', error)
      this.setData({
        relatedMaterials: []
      })
    }
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      // 暂时使用本地存储检查收藏状态
      const favorites = wx.getStorageSync('favorites') || []
      const isFavorited = favorites.some(item => item.id === this.data.materialId)
      this.setData({
        'materialInfo.isFavorited': isFavorited
      })
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  // 添加到浏览历史
  async addToHistory(materialData) {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 优先使用云数据库
        try {
          const historyData = {
            materialId: materialData.id,
            title: materialData.title,
            cover: materialData.cover,
            tags: materialData.tags,
            points: materialData.points,
            viewTime: new Date().getTime()
          }
          
          await app.api.history.addHistory(historyData)
          console.log('浏览历史已添加到云数据库')
        } catch (cloudError) {
          console.error('云数据库添加浏览历史失败，使用本地存储:', cloudError)
          this.addToLocalHistory(materialData)
        }
      } else {
        // 未登录，使用本地存储
        this.addToLocalHistory(materialData)
      }
    } catch (error) {
      console.error('添加浏览历史失败:', error)
    }
  },

  // 添加到本地浏览历史
  addToLocalHistory(materialData) {
    try {
      let viewHistory = wx.getStorageSync('viewHistory') || []
      
      // 移除已存在的相同资料
      viewHistory = viewHistory.filter(item => item.id != materialData.id)
      
      // 添加到开头
      viewHistory.unshift({
        id: materialData.id,
        title: materialData.title,
        cover: materialData.cover,
        tags: materialData.tags,
        points: materialData.points,
        viewTime: new Date().getTime()
      })
      
      // 限制历史记录数量（最多50条）
      if (viewHistory.length > 50) {
        viewHistory = viewHistory.slice(0, 50)
      }
      
      wx.setStorageSync('viewHistory', viewHistory)
      console.log('浏览历史已添加到本地存储')
    } catch (error) {
      console.error('添加本地浏览历史失败:', error)
    }
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    const { previewImages } = this.data.materialInfo
    
    wx.previewImage({
      current: previewImages[index],
      urls: previewImages
    })
  },

  // 切换收藏状态
  async toggleFavorite() {
    const { materialInfo } = this.data
    const newStatus = !materialInfo.isFavorited
    
    try {
      // 使用本地存储管理收藏状态
      let favorites = wx.getStorageSync('favorites') || []
      
      if (newStatus) {
        // 添加收藏
        if (!favorites.some(item => item.id === materialInfo.id)) {
          favorites.push({
            id: materialInfo.id,
            title: materialInfo.title,
            cover: materialInfo.cover,
            points: materialInfo.points,
            favoriteTime: new Date().getTime()
          })
        }
      } else {
        // 取消收藏
        favorites = favorites.filter(item => item.id !== materialInfo.id)
      }
      
      wx.setStorageSync('favorites', favorites)
      
      this.setData({
        'materialInfo.isFavorited': newStatus
      })
      
      wx.showToast({
        title: newStatus ? '收藏成功' : '取消收藏',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('收藏操作失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 分享资源
  shareResource() {
    const { materialInfo } = this.data
    
    return {
      title: materialInfo.title,
      path: `/pages/material-detail/material-detail?id=${materialInfo.id}`,
      imageUrl: materialInfo.cover
    }
  },

  // 下载资源
  async downloadResource() {
    const { materialInfo } = this.data
    
    // 检查用户积分
    const userInfo = app.globalData.userInfo
    if (!userInfo || userInfo.points < materialInfo.points) {
      wx.showModal({
        title: '积分不足',
        content: `下载需要${materialInfo.points}积分，您当前积分不足`,
        showCancel: false
      })
      return
    }
    
    // 确认下载
    wx.showModal({
      title: '确认下载',
      content: `下载将消耗${materialInfo.points}积分，是否继续？`,
      success: async (res) => {
        if (res.confirm) {
          await this.performDownload()
        }
      }
    })
  },

  // 执行下载
  async performDownload() {
    const { materialInfo } = this.data
    
    wx.showLoading({
      title: '下载中...',
      mask: true
    })
    
    try {
      const result = await app.api.material.downloadMaterial(materialInfo.id)
      
      if (result.success) {
        // 更新用户积分
        const userInfo = app.globalData.userInfo
        if (userInfo) {
          userInfo.points -= materialInfo.points
          app.globalData.userInfo = userInfo
        }
        
        wx.hideLoading()
        wx.showToast({
          title: '下载成功',
          icon: 'success'
        })
        
        // 可以在这里处理下载后的逻辑，比如打开文件
        if (result.data.downloadUrl) {
          wx.downloadFile({
            url: result.data.downloadUrl,
            success: (res) => {
              wx.openDocument({
                filePath: res.tempFilePath
              })
            }
          })
        }
      } else {
        wx.hideLoading()
        wx.showToast({
          title: result.message || '下载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('下载失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  },

  // 跳转到相关资料
  goToRelated(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 分享给好友
  onShareAppMessage() {
    return this.shareResource()
  },

  // 分享到朋友圈
  onShareTimeline() {
    return this.shareResource()
  }
})