@echo off
chcp 65001 >nul
title K12管理系统 - 开发服务器

echo ========================================
echo    K12管理系统 - 开发服务器启动脚本
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 正在检查npm环境...
npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到npm，请检查Node.js安装
    pause
    exit /b 1
)

echo.
echo 正在启动开发服务器...
echo 目标端口: 5173
echo 如果端口被占用，将自动杀死占用进程
echo.

npm run start

echo.
echo 开发服务器已关闭
pause