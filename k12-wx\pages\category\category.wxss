/* 分类页面样式 - 现代化设计 */

.container {
  padding-bottom: 200rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
  position: relative;
}

/* 顶部蓝色装饰区域 */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
  z-index: 0;
}

/* 顶部标题区域 */
.page-header {
  padding: 40rpx 32rpx 60rpx;
  color: white;
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 筛选器样式 - 现代卡片设计 */
.filter-section {
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  margin: 0 32rpx 40rpx;
  border-radius: 40rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(22, 119, 255, 0.08);
  border: 2rpx solid rgba(22, 119, 255, 0.06);
  animation: slideInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(-40rpx);
  animation-delay: 0.2s;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-row {
  display: flex;
  margin-bottom: 32rpx;
  gap: 24rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background: linear-gradient(135deg, #F8FAFF 0%, #F0F7FF 100%);
  border-radius: 24rpx;
  border: 2rpx solid rgba(22, 119, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1677FF, #69B1FF);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.filter-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.filter-item.selected {
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  border-color: #1677FF;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.15);
}

.filter-item.selected::before {
  transform: scaleX(1);
}

.filter-item.selected .filter-text {
  color: #1677FF;
  font-weight: 600;
}

.filter-item:active {
  transform: scale(0.98);
}

.filter-item:active::after {
  width: 200rpx;
  height: 200rpx;
}

.filter-text {
  font-size: 30rpx;
  color: #666666;
  font-weight: 500;
}

.filter-arrow {
  font-size: 24rpx;
  color: #999999;
  transition: transform 0.3s ease;
}

.filter-arrow.rotate {
  transform: rotate(180deg);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24rpx;
}

.reset-btn {
  padding: 16rpx 32rpx;
  background-color: transparent;
  color: #1677FF;
  font-size: 28rpx;
  border: 2rpx solid #1677FF;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.reset-btn:active {
  background-color: rgba(22, 119, 255, 0.1);
}

/* 排序区域样式 */
.sort-section {
  display: flex;
  background-color: white;
  padding: 16rpx 32rpx;
  margin-bottom: 16rpx;
  border-bottom: 2rpx solid #E8E8E8;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 24rpx;
  font-size: 28rpx;
  color: #999999;
  border-radius: 16rpx;
  transition: all 0.2s ease;
}

.sort-item.active {
  color: #1677FF;
  background-color: rgba(22, 119, 255, 0.1);
}

.sort-arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
}

/* 资料列表样式 - 现代卡片设计 */
.material-list {
  padding: 0 32rpx;
}

.list-item {
  display: flex;
  padding: 40rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  border-radius: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(22, 119, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(60rpx);
}

/* 错位出现动画 */
.list-item:nth-child(1) { animation-delay: 0.1s; }
.list-item:nth-child(2) { animation-delay: 0.2s; }
.list-item:nth-child(3) { animation-delay: 0.3s; }
.list-item:nth-child(4) { animation-delay: 0.4s; }
.list-item:nth-child(5) { animation-delay: 0.5s; }
.list-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(180deg, #1677FF, #69B1FF);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

/* 涟漪效果 */
.list-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.list-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(22, 119, 255, 0.12);
}

.list-item:active::before {
  transform: scaleY(1);
}

.list-item:active::after {
  width: 600rpx;
  height: 600rpx;
}

.material-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #F0F7FF 0%, #E6F4FF 100%);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  color: #1677FF;
  border: 4rpx solid rgba(22, 119, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.material-cover::after {
  content: '📄';
  position: absolute;
  opacity: 0.3;
}

.material-info {
  flex: 1;
  margin-left: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.material-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.material-tags .tag {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 24rpx;
  border: 2rpx solid rgba(22, 119, 255, 0.15);
  margin: 0;
}

.material-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.material-stats {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #999999;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-item::before {
  content: '👁';
  font-size: 24rpx;
}

.stat-item:first-child::before {
  content: '⬇';
}

.material-price {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF6B35;
  background: linear-gradient(135deg, #FFE7E0 0%, #FFF2EF 100%);
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 107, 53, 0.2);
}

/* 筛选弹窗样式 */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  flex: 0 0 auto;
  min-width: 120rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F0F0F0;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.2s ease;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.filter-option:active {
  background-color: rgba(22, 119, 255, 0.1);
}

.filter-option.active {
  background-color: #1677FF;
  color: #FFFFFF;
}

/* 加载状态样式 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #E8E8E8;
  border-top: 4rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

/* 空状态样式 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #999999;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #CCCCCC;
  opacity: 0.8;
}

/* 加载更多提示 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #999999;
  position: relative;
}

.load-more-text::before,
.load-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 80rpx;
  height: 2rpx;
  background-color: #E8E8E8;
}

.load-more-text::before {
  right: 100%;
  margin-right: 24rpx;
}

.load-more-text::after {
  left: 100%;
  margin-left: 24rpx;
}