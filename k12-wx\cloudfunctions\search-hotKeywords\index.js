// cloudfunctions/search-hotKeywords/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  console.log('获取热门搜索关键词请求:', event)
  
  try {
    // 从系统配置表获取热门搜索关键词
    const configResult = await db.collection('system_configs')
      .where({
        category: 'search',
        key: 'hot_keywords'
      })
      .get()
    
    if (configResult.data.length > 0) {
      const config = configResult.data[0]
      const keywords = config.value.split(',').map(keyword => keyword.trim()).filter(keyword => keyword)
      
      console.log('从配置获取热门搜索:', keywords)
      
      return {
        success: true,
        data: keywords,
        source: 'config'
      }
    }
    
    // 如果配置不存在，返回默认热门搜索关键词
    const defaultKeywords = [
      '期末试卷',
      '单元测试', 
      '练习册',
      '知识点总结',
      '作文素材',
      '数学练习',
      '语文阅读',
      '英语单词',
      '看图写话',
      '口算题卡'
    ]
    
    console.log('使用默认热门搜索关键词')
    
    return {
      success: true,
      data: defaultKeywords,
      source: 'default'
    }
    
  } catch (error) {
    console.error('获取热门搜索关键词失败:', error)
    
    // 最终降级处理
    return {
      success: true,
      data: [
        '期末试卷',
        '单元测试',
        '练习册',
        '知识点总结',
        '作文素材'
      ],
      source: 'fallback'
    }
  }
}
