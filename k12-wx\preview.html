<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K12小程序首页升级预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }
        
        .header {
            padding: 30px 20px;
            color: white;
            text-align: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        
        .app-notice {
            font-size: 12px;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-bottom: 20px;
            display: inline-block;
        }
        
        .search-box {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 12px 20px;
            margin: 0 20px;
            display: flex;
            align-items: center;
            color: #666;
        }
        
        .main-content {
            background: #F8F9FA;
            border-radius: 24px 24px 0 0;
            margin-top: 8px;
            padding: 20px;
            min-height: 500px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 30px;
            height: 3px;
            background: linear-gradient(90deg, #1677FF, #69B1FF);
            border-radius: 2px;
        }
        
        .grade-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 32px;
        }
        
        .grade-item {
            background: linear-gradient(135deg, #FFFFFF 0%, #F8FAFF 100%);
            border-radius: 16px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(22, 119, 255, 0.08);
            border: 1px solid rgba(22, 119, 255, 0.06);
            transition: all 0.3s ease;
        }
        
        .grade-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(22, 119, 255, 0.15);
        }
        
        .grade-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }
        
        .grade-icon-1 { background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%); }
        .grade-icon-2 { background: linear-gradient(135deg, #4ECDC4 0%, #6ED5CE 100%); }
        .grade-icon-3 { background: linear-gradient(135deg, #45B7D1 0%, #67C4DD 100%); }
        .grade-icon-4 { background: linear-gradient(135deg, #96CEB4 0%, #A8D5C1 100%); }
        .grade-icon-5 { background: linear-gradient(135deg, #FFEAA7 0%, #FDCB6E 100%); }
        .grade-icon-6 { background: linear-gradient(135deg, #DDA0DD 0%, #E6B3E6 100%); }
        
        .grade-text {
            font-size: 15px;
            font-weight: 600;
            color: #333;
        }
        
        .upgrade-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .upgrade-item {
            background: linear-gradient(135deg, #FFF3E0 0%, #FFF8F0 100%);
            border-radius: 20px;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 20px rgba(255, 152, 0, 0.15);
            border: 2px solid rgba(255, 152, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .upgrade-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(255, 152, 0, 0.25);
        }
        
        .upgrade-item:nth-child(2) {
            background: linear-gradient(135deg, #E8F5E8 0%, #F0FBF0 100%);
            border-color: rgba(76, 175, 80, 0.3);
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.15);
        }
        
        .upgrade-item:nth-child(2):hover {
            box-shadow: 0 8px 30px rgba(76, 175, 80, 0.25);
        }
        
        .upgrade-icon {
            width: 60px;
            height: 60px;
            border-radius: 18px;
            background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 16px;
            box-shadow: 0 6px 16px rgba(255, 152, 0, 0.3);
        }
        
        .upgrade-item:nth-child(2) .upgrade-icon {
            background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
            box-shadow: 0 6px 16px rgba(76, 175, 80, 0.3);
        }
        
        .upgrade-content {
            flex: 1;
        }
        
        .upgrade-title {
            font-size: 18px;
            font-weight: 700;
            color: #E65100;
            margin-bottom: 4px;
        }
        
        .upgrade-item:nth-child(2) .upgrade-title {
            color: #2E7D32;
        }
        
        .upgrade-subtitle {
            font-size: 14px;
            font-weight: 500;
            color: #BF360C;
            opacity: 0.8;
        }
        
        .upgrade-item:nth-child(2) .upgrade-subtitle {
            color: #1B5E20;
        }
        
        .material-item {
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
            border-radius: 20px;
            padding: 20px;
            display: flex;
            margin-bottom: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(22, 119, 255, 0.08);
        }
        
        .material-cover {
            width: 80px;
            height: 80px;
            border-radius: 16px;
            background: linear-gradient(135deg, #F0F7FF 0%, #E6F4FF 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: #1677FF;
            border: 2px solid rgba(22, 119, 255, 0.1);
            margin-right: 16px;
        }
        
        .material-info {
            flex: 1;
        }
        
        .material-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .material-tags {
            display: flex;
            gap: 6px;
            margin-bottom: 12px;
        }
        
        .tag {
            padding: 4px 10px;
            background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
            color: #1677FF;
            font-size: 12px;
            font-weight: 500;
            border-radius: 12px;
            border: 1px solid rgba(22, 119, 255, 0.15);
        }
        
        .material-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .material-stats {
            display: flex;
            gap: 16px;
            font-size: 13px;
            color: #999;
        }
        
        .material-price {
            font-size: 16px;
            font-weight: 700;
            color: #FF6B35;
            background: linear-gradient(135deg, #FFE7E0 0%, #FFF2EF 100%);
            padding: 6px 12px;
            border-radius: 12px;
            border: 1px solid rgba(255, 107, 53, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部区域 -->
        <div class="header">
            <div class="app-title">小学免费教辅资料</div>
            <div class="app-subtitle">紧跟课表 实时同步 只做精品</div>
            <div class="app-notice">一线教师纯原创资料，侵权必究！</div>
            
            <!-- 搜索框 -->
            <div class="search-box">
                <span>🔍</span>
                <span style="margin-left: 12px; color: #666;">搜索试卷、练习册...</span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 年级分类导航 -->
            <div class="grade-section">
                <div class="section-title">选择年级</div>
                <div class="grade-grid">
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-1">启蒙</div>
                        <div class="grade-text">一年级</div>
                    </div>
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-2">基础</div>
                        <div class="grade-text">二年级</div>
                    </div>
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-3">成长</div>
                        <div class="grade-text">三年级</div>
                    </div>
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-4">进阶</div>
                        <div class="grade-text">四年级</div>
                    </div>
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-5">提升</div>
                        <div class="grade-text">五年级</div>
                    </div>
                    <div class="grade-item">
                        <div class="grade-icon grade-icon-6">冲刺</div>
                        <div class="grade-text">六年级</div>
                    </div>
                </div>
            </div>

            <!-- 升学专区 -->
            <div class="upgrade-section">
                <div class="section-title">升学专区</div>
                <div class="upgrade-grid">
                    <div class="upgrade-item">
                        <div class="upgrade-icon">🎒</div>
                        <div class="upgrade-content">
                            <div class="upgrade-title">幼升小</div>
                            <div class="upgrade-subtitle">入学准备</div>
                        </div>
                    </div>
                    <div class="upgrade-item">
                        <div class="upgrade-icon">🎓</div>
                        <div class="upgrade-content">
                            <div class="upgrade-title">小升初</div>
                            <div class="upgrade-subtitle">升学冲刺</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门资料推荐 -->
            <div class="recommend-section">
                <div class="section-title">热门资料</div>
                <div class="material-item">
                    <div class="material-cover">📚</div>
                    <div class="material-info">
                        <div class="material-title">一年级语文上册期末测试卷</div>
                        <div class="material-tags">
                            <span class="tag">一年级</span>
                            <span class="tag">语文</span>
                            <span class="tag">期末试卷</span>
                        </div>
                        <div class="material-bottom">
                            <div class="material-stats">
                                <span>⬇ 1250</span>
                                <span>👁 3680</span>
                            </div>
                            <div class="material-price">30积分</div>
                        </div>
                    </div>
                </div>
                
                <div class="material-item">
                    <div class="material-cover">📚</div>
                    <div class="material-info">
                        <div class="material-title">二年级数学单元同步练习册</div>
                        <div class="material-tags">
                            <span class="tag">二年级</span>
                            <span class="tag">数学</span>
                            <span class="tag">单元同步</span>
                        </div>
                        <div class="material-bottom">
                            <div class="material-stats">
                                <span>⬇ 980</span>
                                <span>👁 2450</span>
                            </div>
                            <div class="material-price">25积分</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>