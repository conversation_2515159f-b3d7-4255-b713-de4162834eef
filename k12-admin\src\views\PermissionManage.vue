<template>
  <div>
    <!-- 页面标题和操作按钮 -->
    <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
      <h2>权限管理</h2>
      <div>
        <el-button type="primary" @click="handleInitialize" :loading="initializing">
          初始化默认权限
        </el-button>
        <el-button type="success" @click="handleCreatePermission">
          新增权限
        </el-button>
        <el-button type="info" @click="handleCreateRole">
          新增角色
        </el-button>
      </div>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 权限管理标签页 -->
      <el-tab-pane label="权限管理" name="permissions">
        <!-- 权限搜索区域 -->
        <div style="margin-bottom: 20px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="permissionSearchForm.name"
                placeholder="按权限名称搜索"
                clearable
                @input="handlePermissionSearch"
                @clear="handlePermissionSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="permissionSearchForm.category"
                placeholder="按分类筛选"
                clearable
                @change="handlePermissionSearch"
              >
                <el-option label="全部分类" value="" />
                <el-option 
                  v-for="category in permissionCategories" 
                  :key="category" 
                  :label="category" 
                  :value="category" 
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button @click="resetPermissionSearch">重置筛选</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 权限列表表格 -->
        <el-table v-loading="permissionLoading" :data="permissionTableData" style="width: 100%;">
          <el-table-column prop="name" label="权限名称" width="150" />
          <el-table-column prop="code" label="权限代码" width="180" />
          <el-table-column prop="description" label="权限描述" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag type="info">{{ scope.row.category }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="150">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button size="small" @click="handleEditPermission(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDeletePermission(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 角色管理标签页 -->
      <el-tab-pane label="角色管理" name="roles">
        <!-- 角色搜索区域 -->
        <div style="margin-bottom: 20px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="roleSearchForm.name"
                placeholder="按角色名称搜索"
                clearable
                @input="handleRoleSearch"
                @clear="handleRoleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-button @click="resetRoleSearch">重置筛选</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 角色列表表格 -->
        <el-table v-loading="roleLoading" :data="roleTableData" style="width: 100%;">
          <el-table-column prop="name" label="角色名称" width="150" />
          <el-table-column prop="code" label="角色代码" width="150" />
          <el-table-column prop="description" label="角色描述" />
          <el-table-column prop="permissions" label="权限数量" width="100">
            <template #default="scope">
              <el-tag type="success">{{ scope.row.permissions?.length || 0 }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="150">
            <template #default="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button size="small" @click="handleViewRolePermissions(scope.row)">查看权限</el-button>
              <el-button size="small" @click="handleEditRole(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="handleDeleteRole(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 用户权限分配标签页 -->
      <el-tab-pane label="用户权限分配" name="user-permissions">
        <div style="margin-bottom: 20px;">
          <el-input
            v-model="userSearchKeyword"
            placeholder="输入用户昵称或手机号搜索用户"
            style="width: 300px; margin-right: 10px;"
            @input="handleUserSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button @click="handleUserSearch">搜索用户</el-button>
        </div>

        <!-- 用户搜索结果 -->
        <el-table v-loading="userSearchLoading" :data="userSearchResults" style="width: 100%;">
          <el-table-column prop="nickname" label="用户昵称" width="120" />
          <el-table-column prop="phone" label="手机号" width="120" />
          <el-table-column prop="role" label="当前角色" width="100">
            <template #default="scope">
              <el-tag :type="getRoleTagType(scope.row.role)">
                {{ getRoleText(scope.row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleAssignPermissions(scope.row)">
                分配权限
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 权限编辑对话框 -->
    <el-dialog v-model="permissionDialogVisible" :title="permissionDialogTitle" width="500px" @close="resetPermissionForm">
      <el-form :model="permissionForm" label-width="100px" :disabled="permissionUpdating">
        <el-form-item label="权限名称" required>
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限代码" required>
          <el-input v-model="permissionForm.code" placeholder="请输入权限代码，如：user:manage" />
        </el-form-item>
        <el-form-item label="权限描述">
          <el-input v-model="permissionForm.description" type="textarea" placeholder="请输入权限描述" />
        </el-form-item>
        <el-form-item label="权限分类" required>
          <el-select v-model="permissionForm.category" placeholder="请选择或输入分类" filterable allow-create>
            <el-option 
              v-for="category in permissionCategories" 
              :key="category" 
              :label="category" 
              :value="category" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false" :disabled="permissionUpdating">取消</el-button>
          <el-button type="primary" @click="submitPermissionForm" :loading="permissionUpdating">
            {{ permissionUpdating ? '保存中...' : '确认保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 角色编辑对话框 -->
    <el-dialog v-model="roleDialogVisible" :title="roleDialogTitle" width="600px" @close="resetRoleForm">
      <el-form :model="roleForm" label-width="100px" :disabled="roleUpdating">
        <el-form-item label="角色名称" required>
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色代码" required>
          <el-input v-model="roleForm.code" placeholder="请输入角色代码，如：admin" />
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input v-model="roleForm.description" type="textarea" placeholder="请输入角色描述" />
        </el-form-item>
        <el-form-item label="角色权限">
          <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dcdfe6; padding: 10px;">
            <div v-for="category in permissionsByCategory" :key="category.name" style="margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">
                {{ category.name }}
              </div>
              <el-checkbox-group v-model="roleForm.permissions">
                <div v-for="permission in category.permissions" :key="permission._id" style="margin-bottom: 5px;">
                  <el-checkbox :label="permission._id">
                    {{ permission.name }} ({{ permission.code }})
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="roleDialogVisible = false" :disabled="roleUpdating">取消</el-button>
          <el-button type="primary" @click="submitRoleForm" :loading="roleUpdating">
            {{ roleUpdating ? '保存中...' : '确认保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户权限分配对话框 -->
    <el-dialog v-model="userPermissionDialogVisible" title="分配用户权限" width="600px" @close="resetUserPermissionForm">
      <div v-if="selectedUser" style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <div><strong>用户：</strong>{{ selectedUser.nickname }}</div>
        <div><strong>手机号：</strong>{{ selectedUser.phone || '-' }}</div>
        <div><strong>当前角色：</strong>{{ getRoleText(selectedUser.role) }}</div>
      </div>
      
      <el-form :model="userPermissionForm" label-width="100px" :disabled="userPermissionUpdating">
        <el-form-item label="分配角色" required>
          <el-select v-model="userPermissionForm.roleId" placeholder="请选择角色" @change="handleRoleChange">
            <el-option 
              v-for="role in roleTableData" 
              :key="role._id" 
              :label="role.name" 
              :value="role._id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="额外权限">
          <div style="max-height: 250px; overflow-y: auto; border: 1px solid #dcdfe6; padding: 10px;">
            <div v-for="category in permissionsByCategory" :key="category.name" style="margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">
                {{ category.name }}
              </div>
              <el-checkbox-group v-model="userPermissionForm.permissions">
                <div v-for="permission in category.permissions" :key="permission._id" style="margin-bottom: 5px;">
                  <el-checkbox :label="permission._id" :disabled="isPermissionInRole(permission._id)">
                    {{ permission.name }} ({{ permission.code }})
                    <span v-if="isPermissionInRole(permission._id)" style="color: #999; font-size: 12px;">
                      (角色已包含)
                    </span>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userPermissionDialogVisible = false" :disabled="userPermissionUpdating">取消</el-button>
          <el-button type="primary" @click="submitUserPermissionForm" :loading="userPermissionUpdating">
            {{ userPermissionUpdating ? '分配中...' : '确认分配' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 角色权限查看对话框 -->
    <el-dialog v-model="rolePermissionViewVisible" title="角色权限详情" width="500px">
      <div v-if="selectedRole">
        <div style="margin-bottom: 15px; padding: 10px; background-color: #f5f7fa; border-radius: 4px;">
          <div><strong>角色名称：</strong>{{ selectedRole.name }}</div>
          <div><strong>角色代码：</strong>{{ selectedRole.code }}</div>
          <div><strong>角色描述：</strong>{{ selectedRole.description }}</div>
        </div>
        
        <div v-if="selectedRolePermissions.length > 0">
          <h4>拥有权限：</h4>
          <div v-for="category in selectedRolePermissionsByCategory" :key="category.name" style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">
              {{ category.name }}
            </div>
            <div>
              <el-tag 
                v-for="permission in category.permissions" 
                :key="permission._id" 
                style="margin-right: 8px; margin-bottom: 5px;"
                type="success"
              >
                {{ permission.name }}
              </el-tag>
            </div>
          </div>
        </div>
        <div v-else>
          <el-empty description="该角色暂无权限" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { 
  permissionApi,
  roleApi,
  userRoleApi,
  type Permission,
  type Role
} from '../api/permission'
import { getUserList, type User } from '../api/user'

// 响应式数据
const activeTab = ref('permissions')
const initializing = ref(false)

// 权限管理相关数据
const permissionTableData = ref<Permission[]>([])
const permissionLoading = ref(false)
const permissionCategories = ref<string[]>([])
const permissionSearchForm = reactive({
  name: '',
  category: ''
})

// 角色管理相关数据
const roleTableData = ref<Role[]>([])
const roleLoading = ref(false)
const roleSearchForm = reactive({
  name: ''
})

// 用户权限分配相关数据
const userSearchKeyword = ref('')
const userSearchResults = ref<User[]>([])
const userSearchLoading = ref(false)
const selectedUser = ref<User | null>(null)

// 对话框状态
const permissionDialogVisible = ref(false)
const roleDialogVisible = ref(false)
const userPermissionDialogVisible = ref(false)
const rolePermissionViewVisible = ref(false)

// 表单数据
const permissionForm = reactive({
  _id: '',
  name: '',
  code: '',
  description: '',
  category: ''
})

const roleForm = reactive({
  _id: '',
  name: '',
  code: '',
  description: '',
  permissions: [] as string[]
})

const userPermissionForm = reactive({
  roleId: '',
  permissions: [] as string[]
})

// 加载状态
const permissionUpdating = ref(false)
const roleUpdating = ref(false)
const userPermissionUpdating = ref(false)

// 选中的角色和权限
const selectedRole = ref<Role | null>(null)
const selectedRolePermissions = ref<Permission[]>([])

// 计算属性
const permissionDialogTitle = computed(() => {
  return permissionForm._id ? '编辑权限' : '新增权限'
})

const roleDialogTitle = computed(() => {
  return roleForm._id ? '编辑角色' : '新增角色'
})

// 按分类分组的权限
const permissionsByCategory = computed(() => {
  const categories: { [key: string]: Permission[] } = {}
  
  permissionTableData.value.forEach(permission => {
    if (!categories[permission.category]) {
      categories[permission.category] = []
    }
    categories[permission.category].push(permission)
  })
  
  return Object.keys(categories).map(name => ({
    name,
    permissions: categories[name]
  }))
})

// 选中角色的权限按分类分组
const selectedRolePermissionsByCategory = computed(() => {
  const categories: { [key: string]: Permission[] } = {}
  
  selectedRolePermissions.value.forEach(permission => {
    if (!categories[permission.category]) {
      categories[permission.category] = []
    }
    categories[permission.category].push(permission)
  })
  
  return Object.keys(categories).map(name => ({
    name,
    permissions: categories[name]
  }))
})

// 获取权限列表
const fetchPermissions = async () => {
  permissionLoading.value = true
  try {
    const query: any = {}
    
    if (permissionSearchForm.name) {
      query.name = permissionSearchForm.name
    }
    if (permissionSearchForm.category) {
      query.category = permissionSearchForm.category
    }
    
    const result = await permissionApi.getList(query)
    
    if (result.success) {
      permissionTableData.value = result.data
    } else {
      ElMessage.error(result.message || '获取权限列表失败')
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败')
  } finally {
    permissionLoading.value = false
  }
}

// 获取角色列表
const fetchRoles = async () => {
  roleLoading.value = true
  try {
    const query: any = {}
    
    if (roleSearchForm.name) {
      query.name = roleSearchForm.name
    }
    
    const result = await roleApi.getList(query)
    
    if (result.success) {
      roleTableData.value = result.data
    } else {
      ElMessage.error(result.message || '获取角色列表失败')
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
  } finally {
    roleLoading.value = false
  }
}

// 获取权限分类
const fetchPermissionCategories = async () => {
  try {
    // 从权限列表中提取分类
    const categories = [...new Set(permissionTableData.value.map(p => p.category))]
    permissionCategories.value = categories
  } catch (error) {
    console.error('获取权限分类失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'vip': return 'warning'
    default: return 'info'
  }
}

// 获取角色文本
const getRoleText = (role: string) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'vip': return 'VIP用户'
    default: return '普通用户'
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'active': return 'success'
    case 'banned': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'banned': return '封禁'
    default: return '未激活'
  }
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  if (tabName === 'permissions') {
    fetchPermissions()
  } else if (tabName === 'roles') {
    fetchRoles()
  }
}

// 初始化默认权限
const handleInitialize = async () => {
  initializing.value = true
  try {
    // 创建默认权限
    const defaultPermissions = [
      { name: '用户管理', code: 'user:manage', description: '用户管理权限', category: '用户管理' },
      { name: '文件管理', code: 'file:manage', description: '文件管理权限', category: '文件管理' },
      { name: '系统配置', code: 'system:config', description: '系统配置权限', category: '系统管理' }
    ]
    
    for (const permission of defaultPermissions) {
      const permissionData = {
        ...permission,
        type: 'menu' as const,
        status: 'active' as const,
        sort: 0
      }
      await permissionApi.createPermission(permissionData)
    }
    
    ElMessage.success('默认权限初始化成功')
    fetchPermissions()
    fetchRoles()
    fetchPermissionCategories()
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败')
  } finally {
    initializing.value = false
  }
}

// 权限搜索
const handlePermissionSearch = () => {
  fetchPermissions()
}

// 重置权限搜索
const resetPermissionSearch = () => {
  permissionSearchForm.name = ''
  permissionSearchForm.category = ''
  fetchPermissions()
}

// 角色搜索
const handleRoleSearch = () => {
  fetchRoles()
}

// 重置角色搜索
const resetRoleSearch = () => {
  roleSearchForm.name = ''
  fetchRoles()
}

// 用户搜索
const handleUserSearch = async () => {
  if (!userSearchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  userSearchLoading.value = true
  try {
    const query: any = {}
    
    // 根据关键词搜索昵称或手机号
    if (userSearchKeyword.value) {
      query.nickname = userSearchKeyword.value
    }
    
    const result = await getUserList(query, 20, 0)
    
    if (result.success) {
      userSearchResults.value = result.data
    } else {
      ElMessage.error(result.message || '搜索用户失败')
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    userSearchLoading.value = false
  }
}

// 新增权限
const handleCreatePermission = () => {
  resetPermissionForm()
  permissionDialogVisible.value = true
}

// 编辑权限
const handleEditPermission = (row: Permission) => {
  permissionForm._id = row._id || ''
  permissionForm.name = row.name
  permissionForm.code = row.code
  permissionForm.description = row.description || ''
  permissionForm.category = row.category
  permissionDialogVisible.value = true
}

// 删除权限
const handleDeletePermission = async (row: Permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const result = await permissionApi.deletePermission(row._id!)
    
    if (result.success) {
      ElMessage.success('权限删除成功')
      fetchPermissions()
    } else {
      ElMessage.error(result.message || '权限删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除权限失败')
    }
  }
}

// 提交权限表单
const submitPermissionForm = async () => {
  if (!permissionForm.name.trim() || !permissionForm.code.trim() || !permissionForm.category.trim()) {
    ElMessage.warning('请填写必填字段')
    return
  }
  
  permissionUpdating.value = true
  
  try {
    const permissionData = {
      name: permissionForm.name,
      code: permissionForm.code,
      description: permissionForm.description,
      category: permissionForm.category
    }
    
    let result
    if (permissionForm._id) {
      result = await permissionApi.updatePermission(permissionForm._id, permissionData)
    } else {
      const completePermissionData = {
        ...permissionData,
        type: 'menu' as const,
        status: 'active' as const,
        sort: 0
      }
      result = await permissionApi.createPermission(completePermissionData)
    }
    
    if (result.success) {
      ElMessage.success(result.message)
      permissionDialogVisible.value = false
      resetPermissionForm()
      fetchPermissions()
      fetchPermissionCategories()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('权限操作失败:', error)
    ElMessage.error('权限操作失败')
  } finally {
    permissionUpdating.value = false
  }
}

// 重置权限表单
const resetPermissionForm = () => {
  permissionForm._id = ''
  permissionForm.name = ''
  permissionForm.code = ''
  permissionForm.description = ''
  permissionForm.category = ''
}

// 新增角色
const handleCreateRole = () => {
  resetRoleForm()
  roleDialogVisible.value = true
}

// 编辑角色
// 编辑角色
const handleEditRole = async (row: Role) => {
  try {
    // 由于API中没有getRoleDetail，我们直接使用现有数据
    roleForm._id = row._id || ''
    roleForm.name = row.name
    roleForm.code = row.code
    roleForm.description = row.description || ''
    roleForm.permissions = row.permissions || []
    roleDialogVisible.value = true
  } catch (error) {
    console.error('编辑角色失败:', error)
    ElMessage.error('编辑角色失败')
  }
}

// 查看角色权限
const handleViewRolePermissions = async (row: Role) => {
  try {
    selectedRole.value = row
    // 根据权限ID获取权限详情
    const rolePermissions = permissionTableData.value.filter(p => 
      row.permissions?.includes(p._id!)
    )
    selectedRolePermissions.value = rolePermissions
    rolePermissionViewVisible.value = true
  } catch (error) {
    console.error('查看角色权限失败:', error)
    ElMessage.error('查看角色权限失败')
  }
}

// 删除角色
const handleDeleteRole = async (row: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const result = await roleApi.deleteRole(row._id!)
    
    if (result.success) {
      ElMessage.success('角色删除成功')
      fetchRoles()
    } else {
      ElMessage.error(result.message || '角色删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  }
}

// 提交角色表单
const submitRoleForm = async () => {
  if (!roleForm.name.trim() || !roleForm.code.trim()) {
    ElMessage.warning('请填写必填字段')
    return
  }
  
  roleUpdating.value = true
  
  try {
    const roleData = {
      name: roleForm.name,
      code: roleForm.code,
      description: roleForm.description,
      permissions: roleForm.permissions
    }
    
    let result
    if (roleForm._id) {
      result = await roleApi.updateRole(roleForm._id, roleData)
    } else {
      const completeRoleData = {
        ...roleData,
        status: 'active' as const
      }
      result = await roleApi.createRole(completeRoleData)
    }
    
    if (result.success) {
      ElMessage.success(result.message)
      roleDialogVisible.value = false
      resetRoleForm()
      fetchRoles()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('角色操作失败:', error)
    ElMessage.error('角色操作失败')
  } finally {
    roleUpdating.value = false
  }
}

// 重置角色表单
const resetRoleForm = () => {
  roleForm._id = ''
  roleForm.name = ''
  roleForm.code = ''
  roleForm.description = ''
  roleForm.permissions = []
}

// 分配用户权限
const handleAssignPermissions = (user: User) => {
  selectedUser.value = user
  resetUserPermissionForm()
  userPermissionDialogVisible.value = true
}

// 角色变化处理
const handleRoleChange = (roleId: string) => {
  // 清空额外权限选择
  userPermissionForm.permissions = []
}

// 检查权限是否在角色中
const isPermissionInRole = (permissionId: string) => {
  if (!userPermissionForm.roleId) return false
  
  const selectedRole = roleTableData.value.find(role => role._id === userPermissionForm.roleId)
  return selectedRole?.permissions.includes(permissionId) || false
}

// 提交用户权限表单
const submitUserPermissionForm = async () => {
  if (!userPermissionForm.roleId) {
    ElMessage.warning('请选择角色')
    return
  }
  
  if (!selectedUser.value) {
    ElMessage.error('未选择用户')
    return
  }
  
  userPermissionUpdating.value = true
  
  try {
    const result = await userRoleApi.assignRoles(
      selectedUser.value._id!,
      [userPermissionForm.roleId]
    )
    
    if (result.success) {
      ElMessage.success('权限分配成功')
      userPermissionDialogVisible.value = false
      resetUserPermissionForm()
    } else {
      ElMessage.error(result.message || '权限分配失败')
    }
  } catch (error) {
    console.error('权限分配失败:', error)
    ElMessage.error('权限分配失败')
  } finally {
    userPermissionUpdating.value = false
  }
}

// 重置用户权限表单
const resetUserPermissionForm = () => {
  userPermissionForm.roleId = ''
  userPermissionForm.permissions = []
  selectedUser.value = null
}

// 组件挂载时加载数据
onMounted(() => {
  fetchPermissions()
  fetchRoles()
  fetchPermissionCategories()
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
