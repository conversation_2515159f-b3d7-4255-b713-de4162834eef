import request from './request';
import { uploadFileToStorage, createFileRecord, deleteFile, updateFileRecord, getFileById } from './cloudbase';

/**
 * 获取文件列表 - 已迁移到云开发SDK
 * @param query 查询条件（可选）
 * @param limit 限制数量（可选）
 * @param offset 偏移量（可选）
 */
export function getFileList(query: string = '{}', limit: number = 10, offset: number = 0) {
  // 现在直接使用云开发SDK，通过request统一调用
  return request({
    action: 'getFileList',
    payload: { query, limit, offset }
  });
}

/**
 * 新版文件上传流程 - 使用云开发SDK
 * 1. 直接将文件上传到云存储
 * 2. 将文件信息写入数据库
 * @param file 用户选择的文件对象
 * @param fileInfo 文件相关的元数据 (如：名称、描述等)
 * @param onUploadProgress 上传进度回调函数
 */
export async function uploadFile(
  file: File,
  fileInfo: any,
  onUploadProgress?: (progressEvent: any) => void
) {
  try {
    // 为文件生成一个唯一的云端路径，将完整时间戳放在扩展名前
    const generateUniqueFileName = (originalName: string) => {
      const lastDotIndex = originalName.lastIndexOf('.');
      const uniqueId = Date.now().toString(); // 使用完整时间戳作为唯一标识
      
      if (lastDotIndex === -1) {
        // 没有扩展名的文件
        return `${originalName}-${uniqueId}`;
      } else {
        // 有扩展名的文件
        const nameWithoutExt = originalName.substring(0, lastDotIndex);
        const extension = originalName.substring(lastDotIndex);
        return `${nameWithoutExt}-${uniqueId}${extension}`;
      }
    };
    
    const uniqueFileName = generateUniqueFileName(file.name);
    const cloudPath = `uploads/${uniqueFileName}`;

    // 步骤1: 直接使用云开发SDK上传文件到云存储
    const uploadResult = await uploadFileToStorage(file, cloudPath);

    // API 异常处理
    if (uploadResult.code !== 200 || !uploadResult.data) {
      const errMsg = uploadResult.message || '文件上传失败';
      console.error('uploadFileToStorage failed:', uploadResult);
      throw new Error(errMsg);
    }

    const { fileID } = uploadResult.data;

    // 步骤2: 在数据库中创建文件记录
    const fileData = {
      ...fileInfo,
      fileID: fileID,
      cloudPath: cloudPath,
      size: file.size,
      type: file.type,
      status: '已上架',
      downloads: 0,
      // createTime 将由云开发SDK自动添加
    };

    const dbResult = await createFileRecord(fileData);

    if (dbResult.code !== 200) {
      const errMsg = dbResult.message || '创建文件数据库记录失败';
      console.error('createFileRecord failed:', dbResult);
      throw new Error(errMsg);
    }

    return dbResult;

  } catch (error) {
    console.error('文件上传全流程失败:', error);
    // 重新抛出错误，以便UI层可以捕获并显示
    throw error;
  }
}

/**
 * 删除文件 - 修复版本
 * @param fileId 文件ID
 */
export async function deleteFileById(fileId: string) {
  try {
    // 先获取文件详情，获取cloudPath
    const fileDetail = await getFileById(fileId);
    if (fileDetail.code !== 200) {
      throw new Error(fileDetail.message || '获取文件详情失败');
    }
    
    const cloudPath = fileDetail.data.cloudPath || fileDetail.data.fileID;
    
    // 删除文件（包含云存储文件和数据库记录）
    const result = await deleteFile(fileId, cloudPath);
    
    if (result.code !== 200) {
      throw new Error(result.message || '删除文件失败');
    }
    
    return {
      success: true,
      message: '删除成功',
      data: result.data
    };
  } catch (error) {
    console.error('删除文件失败:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '删除文件失败'
    };
  }
}

/**
 * 更新文件信息
 * @param fileId 文件ID
 * @param updateData 更新的数据
 */
export async function updateFile(fileId: string, updateData: any) {
  try {
    const result = await updateFileRecord(fileId, updateData);
    
    if (result.code !== 200) {
      throw new Error(result.message || '更新文件失败');
    }
    
    return {
      success: true,
      message: '更新成功',
      data: result.data
    };
  } catch (error) {
    console.error('更新文件失败:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '更新文件失败'
    };
  }
}

/**
 * 获取文件详情
 * @param fileId 文件ID
 */
export async function getFileDetail(fileId: string) {
  try {
    const result = await getFileById(fileId);
    
    if (result.code !== 200) {
      throw new Error(result.message || '获取文件详情失败');
    }
    
    return {
      success: true,
      message: '获取成功',
      data: result.data
    };
  } catch (error) {
    console.error('获取文件详情失败:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : '获取文件详情失败'
    };
  }
}