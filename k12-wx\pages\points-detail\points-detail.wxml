<!--pages/points-detail/points-detail.wxml-->
<view class="container">
  <!-- 积分概览卡片 -->
  <view class="points-overview">
    <view class="overview-header">
      <text class="header-title">我的积分</text>
      <view class="header-actions">
        <view class="action-btn" bindtap="viewPointsRules">
          <text class="action-text">积分规则</text>
        </view>
      </view>
    </view>
    
    <view class="overview-content">
      <view class="current-points">
        <text class="points-number">{{currentPoints}}</text>
        <text class="points-label">当前积分</text>
      </view>
      
      <view class="points-stats">
        <view class="stat-item">
          <text class="stat-number">{{totalEarned}}</text>
          <text class="stat-label">累计获得</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{totalSpent}}</text>
          <text class="stat-label">累计消费</text>
        </view>
      </view>
    </view>
    
    <view class="overview-actions">
      <view class="overview-btn earn-btn" bindtap="goToEarnPoints">
        <text class="btn-icon">+</text>
        <text class="btn-text">赚取积分</text>
      </view>
      <view class="overview-btn mall-btn" bindtap="goToPointsMall">
        <text class="btn-icon">🛒</text>
        <text class="btn-text">积分商城</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <view class="tab-item {{filterType === 'all' ? 'active' : ''}}" bindtap="onFilterChange" data-type="all">
      <text>全部</text>
    </view>
    <view class="tab-item {{filterType === 'earn' ? 'active' : ''}}" bindtap="onFilterChange" data-type="earn">
      <text>获得</text>
    </view>
    <view class="tab-item {{filterType === 'spend' ? 'active' : ''}}" bindtap="onFilterChange" data-type="spend">
      <text>消费</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{isEmpty && !loading}}">
    <image class="empty-icon" src="/images/empty-points.png" mode="aspectFit"></image>
    <text class="empty-title">暂无积分记录</text>
    <text class="empty-subtitle">快去完成任务赚取积分吧</text>
    <view class="empty-button" bindtap="goToEarnPoints">
      <text>去赚积分</text>
    </view>
  </view>

  <!-- 积分历史列表 -->
  <view class="history-list" wx:if="{{!isEmpty && !loading}}">
    <view class="history-item" wx:for="{{pointsHistory}}" wx:key="id">
      <view class="item-icon {{item.type}}">
        <text class="icon-text" wx:if="{{item.type === 'earn'}}">+</text>
        <text class="icon-text" wx:if="{{item.type === 'spend'}}">-</text>
      </view>
      
      <view class="item-content">
        <view class="item-header">
          <text class="item-title">{{item.title}}</text>
          <text class="item-points {{item.type}}">
            {{item.type === 'earn' ? '+' : ''}}{{item.points}}
          </text>
        </view>
        <view class="item-description">{{item.description}}</view>
        <view class="item-time">{{item.time}}</view>
      </view>
    </view>
  </view>

  <!-- 加载更多提示 -->
  <view class="load-more" wx:if="{{!isEmpty && !loading && !hasMore}}">
    <text class="load-more-text">没有更多记录了</text>
  </view>
</view>