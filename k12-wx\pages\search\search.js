// 搜索页面逻辑
const app = getApp()

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchFocus: true,
    searchHistory: [],
    hotKeywords: [], // 改为空数组，从服务器获取
    searchSuggestions: [], // 搜索建议
    showSuggestions: false, // 是否显示搜索建议
    
    // 搜索结果
    searchResults: [],
    totalCount: 0,
    loading: false,
    loadingMore: false, // 加载更多状态
    hasMore: true, // 是否还有更多数据
    currentPage: 1, // 当前页码
    pageSize: 20, // 每页数量
    
  },

  onLoad(options) {
    // 从其他页面传入的搜索关键词
    if (options.keyword) {
      this.setData({
        searchKeyword: options.keyword,
        searchFocus: false
      })
      this.performSearch()
    }
    
    // 加载搜索历史
    this.loadSearchHistory()
    
    // 加载热门搜索
    this.loadHotKeywords()
  },

  onShow() {
    // 页面显示时重新获取焦点
    if (!this.data.searchKeyword) {
      this.setData({ searchFocus: true })
    }
  },

  // 页面触底事件
  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore && this.data.searchKeyword) {
      this.loadMoreResults()
    }
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({ searchHistory: history.slice(0, 10) }) // 最多显示10条
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  },

  // 加载热门搜索关键词
  async loadHotKeywords() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'search-hotKeywords'
      })
      
      if (result.result && result.result.success) {
        this.setData({
          hotKeywords: result.result.data || []
        })
      } else {
        // 使用默认热门搜索关键词
        this.setData({
          hotKeywords: ['期末试卷', '单元测试', '练习册', '知识点总结', '作文素材']
        })
      }
    } catch (error) {
      console.error('加载热门搜索关键词失败:', error)
      // 使用默认热门搜索关键词
      this.setData({
        hotKeywords: ['期末试卷', '单元测试', '练习册', '知识点总结', '作文素材']
      })
    }
  },

  // 获取搜索建议
  async getSearchSuggestions(keyword) {
    if (!keyword || keyword.length < 2) {
      this.setData({
        searchSuggestions: [],
        showSuggestions: false
      })
      return
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'search-suggestions',
        data: { keyword }
      })
      
      if (result.result && result.result.success) {
        this.setData({
          searchSuggestions: result.result.data || [],
          showSuggestions: result.result.data && result.result.data.length > 0
        })
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error)
    }
  },


  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return
    
    try {
      let history = wx.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制数量
      history = history.slice(0, 20)
      
      wx.setStorageSync('searchHistory', history)
      this.setData({ searchHistory: history.slice(0, 10) })
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword
    })
    
    // 获取搜索建议
    this.getSearchSuggestions(keyword)
  },

  // 执行搜索
  onSearch() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) return
    
    this.setData({
      showSuggestions: false
    })
    
    this.saveSearchHistory(keyword)
    this.performSearch(true) // 重新搜索
  },

  // 执行搜索请求
  async performSearch(reset = false) {
    const { searchKeyword, currentPage, pageSize } = this.data
    
    if (!searchKeyword.trim()) return
    
    if (reset) {
      this.setData({ 
        loading: true,
        currentPage: 1,
        searchResults: [],
        hasMore: true
      })
    } else {
      this.setData({ loadingMore: true })
    }
    
    try {
      const params = {
        keyword: searchKeyword,
        page: reset ? 1 : currentPage,
        pageSize: pageSize
      }
      
      const result = await wx.cloud.callFunction({
        name: 'search-materials',
        data: params
      })
      
      if (result.result && result.result.success) {
        const data = result.result.data
        const newResults = data.list || []
        
        this.setData({
          searchResults: reset ? newResults : [...this.data.searchResults, ...newResults],
          totalCount: data.pagination ? data.pagination.total : 0,
          currentPage: reset ? 2 : currentPage + 1,
          hasMore: newResults.length === pageSize
        })
      } else {
        this.setData({
          searchResults: reset ? [] : this.data.searchResults,
          totalCount: reset ? 0 : this.data.totalCount,
          hasMore: false
        })
        
        if (reset) {
          wx.showToast({
            title: result.result?.message || '搜索失败',
            icon: 'none'
          })
        }
      }
    } catch (error) {
      console.error('搜索失败:', error)
      
      if (reset) {
        this.setData({
          searchResults: [],
          totalCount: 0
        })
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
      
      this.setData({ hasMore: false })
    } finally {
      this.setData({ 
        loading: false,
        loadingMore: false
      })
    }
  },

  // 加载更多结果
  loadMoreResults() {
    this.performSearch(false)
  },

  // 清空搜索
  clearSearch() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      totalCount: 0,
      searchFocus: true,
      showSuggestions: false,
      currentPage: 1,
      hasMore: true
    })
  },

  // 选择搜索建议
  selectSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      searchFocus: false,
      showSuggestions: false
    })
    this.performSearch(true)
  },

  // 选择历史搜索
  selectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      searchFocus: false,
      showSuggestions: false
    })
    this.performSearch(true)
  },

  // 选择热门关键词
  selectHotKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      searchKeyword: keyword,
      searchFocus: false,
      showSuggestions: false
    })
    this.performSearch(true)
  },

  // 清空搜索历史
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory')
          this.setData({ searchHistory: [] })
        }
      }
    })
  },


  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    console.log('跳转到详情页，ID:', id)
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})