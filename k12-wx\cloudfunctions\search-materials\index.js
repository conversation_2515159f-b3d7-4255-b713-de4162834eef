// cloudfunctions/search-materials/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  console.log('搜索资料请求:', event)
  
  try {
    const { 
      keyword = '', 
      grade = '', 
      subject = '', 
      semester = '', 
      category = '',
      page = 1, 
      pageSize = 20,
      sortBy = 'upload_time',
      sortOrder = 'desc'
    } = event
    
    // 构建查询条件 - 修改为匹配实际数据库字段
    let whereCondition = {
      status: 'active'  // 实际数据库中status字段值是'active'
    }
    
    // 智能分层搜索 - 分步查询确保相关度排序
    if (keyword && keyword.trim()) {
      // 将关键词按空格、中文标点等分割
      const keywords = keyword.trim().split(/[\s,，、。；;]+/).filter(k => k.length > 0)
      
      let keywordCondition = null
      
      if (keywords.length === 1) {
        // 单关键词搜索 - 使用原有逻辑
        keywordCondition = _.or([
          { title: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { description: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { grade: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { subject: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { volume: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { section: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { tags: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { file_type: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { upgradeType: db.RegExp({ regexp: keywords[0], options: 'i' })},
          { upgradeCategory: db.RegExp({ regexp: keywords[0], options: 'i' })}
        ])
      } else {
        // 多关键词智能搜索 - 优先AND匹配，确保精确度
        const keywordConditions = keywords.map(kw => {
          return _.or([
            { title: db.RegExp({ regexp: kw, options: 'i' })},
            { description: db.RegExp({ regexp: kw, options: 'i' })},
            { grade: db.RegExp({ regexp: kw, options: 'i' })},
            { subject: db.RegExp({ regexp: kw, options: 'i' })},
            { volume: db.RegExp({ regexp: kw, options: 'i' })},
            { section: db.RegExp({ regexp: kw, options: 'i' })},
            { tags: db.RegExp({ regexp: kw, options: 'i' })}
          ])
        })
        
        // 使用AND逻辑确保所有关键词都匹配
        keywordCondition = _.and(keywordConditions)
        
        console.log('多关键词AND搜索:', {
          原始关键词: keyword,
          拆分关键词: keywords,
          搜索策略: 'AND逻辑 - 所有关键词必须匹配'
        })
      }
      
      whereCondition = _.and([whereCondition, keywordCondition])
    }
    
    // 分类筛选 - 使用实际的数据库字段
    if (grade) {
      whereCondition.grade = grade
    }
    if (subject) {
      whereCondition.subject = subject
    }
    if (semester) {
      // semester对应实际数据库中的volume字段
      const volumeMap = {
        '上学期': '上册',
        '下学期': '下册',
        '全年': '全册'
      }
      const volume = volumeMap[semester] || semester
      if (volume) {
        whereCondition.volume = volume
      }
    }
    if (category) {
      // category对应实际数据库中的section字段
      whereCondition.section = category
    }
    
    console.log('查询条件:', whereCondition)
    
    // 计算分页
    const skip = (page - 1) * pageSize
    
    // 构建排序条件 - 处理字段映射
    let actualSortBy = sortBy
    if (sortBy === 'download_count') {
      actualSortBy = 'points' // 映射废弃字段到新字段
    }
    
    // 执行查询
    const result = await db.collection('files')
      .where(whereCondition)
      .orderBy(actualSortBy, sortOrder === 'desc' ? 'desc' : 'asc')
      .skip(skip)
      .limit(pageSize)
      .field({
        _id: true,
        title: true,
        grade: true,
        subject: true,
        volume: true,
        section: true,
        points: true,
        viewCount: true,
        favoriteCount: true,
        fileUrl: true,
        fileSize: true,
        uploadTime: true,
        status: true,
        preview_images: true,
        upgradeType: true,
        upgradeCategory: true,
        tags: true
      })
      .get()
    
    // 获取总数
    const countResult = await db.collection('files')
      .where(whereCondition)
      .count()
    
    console.log(`搜索结果: 找到${result.data.length}条记录，总计${countResult.total}条`)
    
    // 记录搜索历史（如果有关键词）
    if (keyword && keyword.trim()) {
      try {
        await db.collection('search_history').add({
          data: {
            keyword: keyword.trim(),
            user_id: event.userInfo ? event.userInfo.openId : 'anonymous',
            search_time: new Date(),
            result_count: countResult.total,
            filters: {
              grade,
              subject, 
              semester,
              category
            }
          }
        })
      } catch (historyError) {
        console.warn('记录搜索历史失败:', historyError)
        // 不影响主要搜索功能
      }
    }
    
    return {
      success: true,
      data: {
        list: result.data,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: countResult.total,
          totalPages: Math.ceil(countResult.total / pageSize)
        },
        filters: {
          keyword,
          grade,
          subject,
          semester,
          category
        },
        sort: {
          sortBy,
          sortOrder
        }
      }
    }
    
  } catch (error) {
    console.error('搜索资料失败:', error)
    
    return {
      success: false,
      message: '搜索失败，请稍后重试',
      error: error.message,
      data: {
        list: [],
        pagination: {
          page: 1,
          pageSize: 20,
          total: 0,
          totalPages: 0
        }
      }
    }
  }
}