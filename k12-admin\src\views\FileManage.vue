<template>
  <div>
    <div style="margin-bottom: 20px;">
      <el-button type="primary" @click="handleUpload">上传文件</el-button>
      
      <!-- 批量操作按钮组 -->
      <div v-if="selectedFiles.length > 0" style="display: inline-block; margin-left: 20px;">
        <el-tag type="info" style="margin-right: 10px;">
          已选择 {{ selectedFiles.length }} 个文件
        </el-tag>
        <el-button size="small" type="danger" @click="handleBatchDelete" :loading="batchDeleting">
          批量删除
        </el-button>
        <el-button size="small" @click="handleBatchStatusChange('已上架')" :loading="batchUpdating">
          批量上架
        </el-button>
        <el-button size="small" @click="handleBatchStatusChange('已下架')" :loading="batchUpdating">
          批量下架
        </el-button>
        <el-button size="small" @click="clearSelection">取消选择</el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div style="margin-bottom: 20px; padding: 16px; background-color: #f5f7fa; border-radius: 4px;">
      <el-row :gutter="20" style="margin-bottom: 16px;">
        <el-col :span="8">
          <el-input
            v-model="searchForm.title"
            placeholder="按标题搜索"
            clearable
            @input="handleSearch"
            @clear="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.grade"
            placeholder="按年级筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部年级" value="" />
            <el-option 
              v-for="grade in gradeOptions" 
              :key="grade._id" 
              :label="grade.name" 
              :value="grade.name" 
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.subject"
            placeholder="按科目筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部科目" value="" />
            <el-option 
              v-for="subject in subjectOptions" 
              :key="subject._id" 
              :label="subject.name" 
              :value="subject.name" 
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.volume"
            placeholder="按册别筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部册别" value="" />
            <el-option 
              v-for="volume in volumeOptions" 
              :key="volume._id" 
              :label="volume.name" 
              :value="volume.name" 
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.section"
            placeholder="按板块筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部板块" value="" />
            <el-option 
              v-for="section in sectionOptions" 
              :key="section._id" 
              :label="section.name" 
              :value="section.name" 
            />
          </el-select>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="按状态筛选"
            clearable
            @change="handleSearch"
          >
            <el-option label="全部状态" value="" />
            <el-option label="已上架" value="已上架" />
            <el-option label="已下架" value="已下架" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetSearch">重置筛选</el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </el-col>
      </el-row>
    </div>
    
    <el-table 
      ref="tableRef"
      v-loading="loading" 
      :data="tableData" 
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" label="标题" width="180" />
      <el-table-column prop="grade" label="年级" width="80" />
      <el-table-column prop="subject" label="科目" width="80" />
      <el-table-column prop="volume" label="册别" width="80" />
      <el-table-column prop="section" label="板块" width="100" />
      <el-table-column prop="points" label="积分" width="80">
        <template #default="scope">
          <el-tag type="warning">{{ scope.row.points || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '已上架' ? 'success' : 'warning'">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="uploadTime" label="上传时间" width="150">
        <template #default="scope">
          {{ formatDate(scope.row.uploadTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="downloads" label="下载量" width="80" />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" title="上传文件" width="600" @close="resetForm">
      <el-form :model="form" label-width="80px" :disabled="uploading">
        <el-form-item label="文件标题">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年级">
              <el-select v-model="form.grade" placeholder="请选择年级">
                <el-option 
                  v-for="grade in gradeOptions" 
                  :key="grade._id" 
                  :label="grade.name" 
                  :value="grade.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="科目">
              <el-select v-model="form.subject" placeholder="请选择科目">
                <el-option 
                  v-for="subject in subjectOptions" 
                  :key="subject._id" 
                  :label="subject.name" 
                  :value="subject.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="册别">
              <el-select v-model="form.volume" placeholder="请选择册别">
                <el-option 
                  v-for="volume in volumeOptions" 
                  :key="volume._id" 
                  :label="volume.name" 
                  :value="volume.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="板块">
              <el-select v-model="form.section" placeholder="请选择板块">
                <el-option 
                  v-for="section in sectionOptions" 
                  :key="section._id" 
                  :label="section.name" 
                  :value="section.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="积分价格">
          <el-input-number v-model="form.points" :min="0" :max="1000" />
        </el-form-item>
        <el-form-item label="选择文件">
          <el-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :http-request="handleHttpRequest"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                仅支持单个文件上传
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <div v-if="uploading" style="margin-top: 15px;">
        <el-progress :percentage="uploadProgress" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="uploading">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            {{ uploading ? '上传中...' : '确认上传' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑文件对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑文件" width="600" @close="resetEditForm">
      <el-form :model="editForm" label-width="80px" :disabled="updating">
        <el-form-item label="文件标题">
          <el-input v-model="editForm.title" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年级">
              <el-select v-model="editForm.grade" placeholder="请选择年级">
                <el-option 
                  v-for="grade in gradeOptions" 
                  :key="grade._id" 
                  :label="grade.name" 
                  :value="grade.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="科目">
              <el-select v-model="editForm.subject" placeholder="请选择科目">
                <el-option 
                  v-for="subject in subjectOptions" 
                  :key="subject._id" 
                  :label="subject.name" 
                  :value="subject.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="册别">
              <el-select v-model="editForm.volume" placeholder="请选择册别">
                <el-option 
                  v-for="volume in volumeOptions" 
                  :key="volume._id" 
                  :label="volume.name" 
                  :value="volume.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="板块">
              <el-select v-model="editForm.section" placeholder="请选择板块">
                <el-option 
                  v-for="section in sectionOptions" 
                  :key="section._id" 
                  :label="section.name" 
                  :value="section.name" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="积分价格">
          <el-input-number v-model="editForm.points" :min="0" :max="1000" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="已上架" value="已上架" />
            <el-option label="已下架" value="已下架" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false" :disabled="updating">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="updating">
            {{ updating ? '更新中...' : '确认更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getFileList, uploadFile, deleteFileById, updateFile, getFileDetail } from '../api/file'
import { getCategoryList } from '../api/category'
import type { UploadInstance, UploadUserFile, UploadRequestOptions } from 'element-plus'

const dialogVisible = ref(false)
const editDialogVisible = ref(false)
const tableData = ref([])
const loading = ref(true)
const uploading = ref(false)
const updating = ref(false)
const uploadProgress = ref(0)
const uploadRef = ref<UploadInstance>()
const tableRef = ref()
const fileList = ref<UploadUserFile[]>([])
const currentEditId = ref('')
const selectedFiles = ref([])
const batchDeleting = ref(false)
const batchUpdating = ref(false)
const originalTableData = ref([]) // 存储原始数据用于筛选

// 分类数据
const categories = ref([])
const loadingCategories = ref(false)

// 按类型分组的分类选项
const gradeOptions = computed(() => 
  categories.value.filter(cat => cat.type === 'grade').sort((a, b) => a.order - b.order)
)
const subjectOptions = computed(() => 
  categories.value.filter(cat => cat.type === 'subject').sort((a, b) => a.order - b.order)
)
const volumeOptions = computed(() => 
  categories.value.filter(cat => cat.type === 'volume').sort((a, b) => a.order - b.order)
)
const sectionOptions = computed(() => 
  categories.value.filter(cat => cat.type === 'section').sort((a, b) => a.order - b.order)
)

// 获取分类数据
const fetchCategories = async () => {
  loadingCategories.value = true
  try {
    const response = await getCategoryList()
    if (response.success) {
      categories.value = response.data
    } else {
      console.error('获取分类列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  } finally {
    loadingCategories.value = false
  }
}

// 搜索表单
const searchForm = reactive({
  title: '',
  grade: '',
  subject: '',
  volume: '',
  section: '',
  status: '',
  dateRange: null as [string, string] | null
})

const form = reactive({
  title: '',
  grade: '',
  subject: '',
  volume: '',
  section: '',
  points: 0
})

const editForm = reactive({
  title: '',
  grade: '',
  subject: '',
  volume: '',
  section: '',
  points: 0,
  status: ''
})

const fetchFiles = async () => {
  loading.value = true
  try {
    const res: any = await getFileList('{}', 100, 0) // 增加获取数量以支持本地筛选
    const allData = res.Data.map((item: string) => JSON.parse(item))
    originalTableData.value = allData
    tableData.value = allData
    // 如果有搜索条件，应用筛选
    if (hasSearchConditions()) {
      applyFilters()
    }
  } catch (error) {
    console.error("获取文件列表失败:", error)
    ElMessage.error('获取文件列表失败')
  } finally {
    loading.value = false
  }
}

// 检查是否有搜索条件
const hasSearchConditions = () => {
  return searchForm.title || 
         searchForm.grade || 
         searchForm.subject || 
         searchForm.volume || 
         searchForm.section || 
         searchForm.status || 
         searchForm.dateRange
}

// 应用筛选条件
const applyFilters = () => {
  let filteredData = [...originalTableData.value]

  // 按标题搜索
  if (searchForm.title) {
    filteredData = filteredData.filter(item => 
      item.title.toLowerCase().includes(searchForm.title.toLowerCase())
    )
  }

  // 按年级筛选
  if (searchForm.grade) {
    filteredData = filteredData.filter(item => item.grade === searchForm.grade)
  }

  // 按科目筛选
  if (searchForm.subject) {
    filteredData = filteredData.filter(item => item.subject === searchForm.subject)
  }

  // 按册别筛选
  if (searchForm.volume) {
    filteredData = filteredData.filter(item => item.volume === searchForm.volume)
  }

  // 按板块筛选
  if (searchForm.section) {
    filteredData = filteredData.filter(item => item.section === searchForm.section)
  }

  // 按状态筛选
  if (searchForm.status) {
    filteredData = filteredData.filter(item => item.status === searchForm.status)
  }

  // 按上传时间筛选
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    const [startDate, endDate] = searchForm.dateRange
    filteredData = filteredData.filter(item => {
      if (!item.uploadTime) return false
      const uploadDate = new Date(item.uploadTime).toISOString().split('T')[0]
      return uploadDate >= startDate && uploadDate <= endDate
    })
  }

  tableData.value = filteredData
}

// 处理搜索
const handleSearch = () => {
  applyFilters()
  // 清除选择
  clearSelection()
}

// 重置搜索
const resetSearch = () => {
  searchForm.title = ''
  searchForm.grade = ''
  searchForm.subject = ''
  searchForm.volume = ''
  searchForm.section = ''
  searchForm.status = ''
  searchForm.dateRange = null
  tableData.value = [...originalTableData.value]
  clearSelection()
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

onMounted(() => {
  fetchCategories()
  fetchFiles()
})

const handleUpload = () => {
  dialogVisible.value = true
}

const resetForm = () => {
  form.title = ''
  form.grade = ''
  form.subject = ''
  form.volume = ''
  form.section = ''
  form.points = 0
  uploadProgress.value = 0
  uploadRef.value?.clearFiles()
}

// 使用 el-upload 的 http-request 属性，完全接管上传过程
const handleHttpRequest = async (options: UploadRequestOptions) => {
  const { file, onProgress, onSuccess, onError } = options;

  uploading.value = true;
  uploadProgress.value = 0;

  try {
    // 调用我们封装好的三步上传函数，包含四维度分类数据
    await uploadFile(
      file,
      { 
        title: form.title,
        grade: form.grade,
        subject: form.subject,
        volume: form.volume,
        section: form.section,
        points: form.points
      },
      (progressEvent: any) => {
        // 更新本地进度条
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        uploadProgress.value = percent;
        // 调用 el-upload 内置的 onProgress，使其自带的进度条也能工作
        onProgress({ percent });
      }
    );

    // 上传成功
    onSuccess({});
    dialogVisible.value = false;
    await fetchFiles(); // 重新加载列表
    ElMessage.success('上传成功');

  } catch (error: any) {
    const errorMessage = error?.message || '上传失败';
    ElMessage.error(errorMessage);
    console.error('Upload failed:', error);
    onError(error);
  } finally {
    uploading.value = false;
  }
}

// "确认上传"按钮的点击事件
const submitUpload = () => {
  if (!form.title) {
    ElMessage.error('请填写文件标题');
    return;
  }
  if (!form.grade) {
    ElMessage.error('请选择年级');
    return;
  }
  if (!form.subject) {
    ElMessage.error('请选择科目');
    return;
  }
  if (!form.volume) {
    ElMessage.error('请选择册别');
    return;
  }
  if (!form.section) {
    ElMessage.error('请选择板块');
    return;
  }
  // 调用 el-upload 的 submit 方法，这会触发 http-request
  uploadRef.value?.submit();
}

// 处理编辑操作
const handleEdit = async (row: any) => {
  try {
    const response = await getFileDetail(row._id)
    if (response.success) {
      const fileData = response.data
      editForm.title = fileData.title
      editForm.grade = fileData.grade || ''
      editForm.subject = fileData.subject || ''
      editForm.volume = fileData.volume || ''
      editForm.section = fileData.section || ''
      editForm.points = fileData.points || 0
      editForm.status = fileData.status || '已上架'
      currentEditId.value = row._id
      editDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取文件详情失败')
    }
  } catch (error) {
    console.error('获取文件详情失败:', error)
    ElMessage.error('获取文件详情失败')
  }
}

// 提交编辑
const submitEdit = async () => {
  if (!editForm.title.trim()) {
    ElMessage.warning('请输入文件标题')
    return
  }
  
  if (!editForm.grade) {
    ElMessage.warning('请选择年级')
    return
  }
  
  if (!editForm.subject) {
    ElMessage.warning('请选择科目')
    return
  }
  
  if (!editForm.volume) {
    ElMessage.warning('请选择册别')
    return
  }
  
  if (!editForm.section) {
    ElMessage.warning('请选择板块')
    return
  }
  
  updating.value = true
  
  try {
    const response = await updateFile(currentEditId.value, {
      title: editForm.title,
      grade: editForm.grade,
      subject: editForm.subject,
      volume: editForm.volume,
      section: editForm.section,
      points: editForm.points,
      status: editForm.status
    })
    
    if (response.success) {
      ElMessage.success('文件更新成功')
      editDialogVisible.value = false
      resetEditForm()
      fetchFiles()
    } else {
      ElMessage.error(response.message || '文件更新失败')
    }
  } catch (error) {
    console.error('文件更新失败:', error)
    ElMessage.error('文件更新失败')
  } finally {
    updating.value = false
  }
}

// 处理删除操作
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${row.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 显示删除中的加载状态
    const loadingMessage = ElMessage({
      message: '正在删除文件...',
      type: 'info',
      duration: 0
    })
    
    try {
      const response = await deleteFileById(row._id)
      loadingMessage.close()
      
      if (response.success) {
        ElMessage.success('文件删除成功')
        // 确保删除成功后刷新列表
        await fetchFiles()
      } else {
        ElMessage.error(response.message || '文件删除失败')
      }
    } catch (deleteError) {
      loadingMessage.close()
      console.error('文件删除失败:', deleteError)
      ElMessage.error('文件删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除操作失败:', error)
      ElMessage.error('删除操作失败')
    }
  }
}

// 重置编辑表单
const resetEditForm = () => {
  editForm.title = ''
  editForm.grade = ''
  editForm.subject = ''
  editForm.volume = ''
  editForm.section = ''
  editForm.points = 0
  editForm.status = ''
  currentEditId.value = ''
}

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedFiles.value = selection
}

// 清除选择
const clearSelection = () => {
  tableRef.value?.clearSelection()
  selectedFiles.value = []
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    batchDeleting.value = true
    let successCount = 0
    let failCount = 0
    const totalCount = selectedFiles.value.length

    // 显示进度消息
    const progressMessage = ElMessage({
      message: `正在删除文件 (0/${totalCount})...`,
      type: 'info',
      duration: 0
    })

    // 逐个删除文件
    for (let i = 0; i < selectedFiles.value.length; i++) {
      const file = selectedFiles.value[i]
      try {
        // 更新进度消息
        progressMessage.close()
        const newProgressMessage = ElMessage({
          message: `正在删除文件 (${i + 1}/${totalCount}): ${file.title}`,
          type: 'info',
          duration: 0
        })

        const response = await deleteFileById(file._id)
        newProgressMessage.close()

        if (response.success) {
          successCount++
        } else {
          failCount++
          console.error(`删除文件 ${file.title} 失败:`, response.message)
        }
      } catch (error) {
        failCount++
        console.error(`删除文件 ${file.title} 失败:`, error)
      }
    }

    // 显示结果
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`批量删除成功，共删除 ${successCount} 个文件`)
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`批量删除完成，成功 ${successCount} 个，失败 ${failCount} 个`)
    } else {
      ElMessage.error(`批量删除失败，共 ${failCount} 个文件删除失败`)
    }

    // 清除选择并刷新列表
    clearSelection()
    await fetchFiles()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除操作失败:', error)
      ElMessage.error('批量删除操作失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

// 批量状态修改
const handleBatchStatusChange = async (newStatus: string) => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要修改的文件')
    return
  }

  const actionText = newStatus === '已上架' ? '上架' : '下架'
  
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedFiles.value.length} 个文件${actionText}吗？`,
      `批量${actionText}确认`,
      {
        confirmButtonText: `确定${actionText}`,
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    batchUpdating.value = true
    let successCount = 0
    let failCount = 0
    const totalCount = selectedFiles.value.length

    // 显示进度消息
    const progressMessage = ElMessage({
      message: `正在${actionText}文件 (0/${totalCount})...`,
      type: 'info',
      duration: 0
    })

    // 逐个更新文件状态
    for (let i = 0; i < selectedFiles.value.length; i++) {
      const file = selectedFiles.value[i]
      try {
        // 更新进度消息
        progressMessage.close()
        const newProgressMessage = ElMessage({
          message: `正在${actionText}文件 (${i + 1}/${totalCount}): ${file.title}`,
          type: 'info',
          duration: 0
        })

        const response = await updateFile(file._id, {
          status: newStatus
        })
        newProgressMessage.close()

        if (response.success) {
          successCount++
        } else {
          failCount++
          console.error(`${actionText}文件 ${file.title} 失败:`, response.message)
        }
      } catch (error) {
        failCount++
        console.error(`${actionText}文件 ${file.title} 失败:`, error)
      }
    }

    // 显示结果
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`批量${actionText}成功，共${actionText} ${successCount} 个文件`)
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`批量${actionText}完成，成功 ${successCount} 个，失败 ${failCount} 个`)
    } else {
      ElMessage.error(`批量${actionText}失败，共 ${failCount} 个文件${actionText}失败`)
    }

    // 清除选择并刷新列表
    clearSelection()
    await fetchFiles()

  } catch (error) {
    if (error !== 'cancel') {
      console.error(`批量${actionText}操作失败:`, error)
      ElMessage.error(`批量${actionText}操作失败`)
    }
  } finally {
    batchUpdating.value = false
  }
}
</script>
