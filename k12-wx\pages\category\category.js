// 分类页面逻辑
const app = getApp()

Page({
  data: {
    // 筛选条件
    selectedGrade: '',
    selectedSubject: '',
    selectedVolume: '',
    selectedCategory: '',
    
    // 排序条件
    sortType: 'download', // download, time, points
    sortOrder: 'desc', // desc, asc
    
    // 筛选选项 - 从数据库动态加载
    gradeOptions: [],
    subjectOptions: [],
    volumeOptions: [],
    categoryOptions: [],
    
    // 弹窗显示状态
    showGradePopup: false,
    showSubjectPopup: false,
    showVolumePopup: false,
    showCategoryPopup: false,
    
    // 资料列表
    materialList: [],
    loading: false,
    page: 1,
    hasMore: true,
    
    // 筛选条件记忆
    filterMemory: {}
  },

  onLoad(options) {
    // 从其他页面传入的筛选条件
    if (options.grade) {
      this.setData({ selectedGrade: options.grade })
    }
    if (options.subject) {
      this.setData({ selectedSubject: options.subject })
    }
    
    // 恢复筛选条件记忆
    this.restoreFilterMemory()
    
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '资料分类'
    })
    
    // 先加载筛选选项，再加载资料列表
    this.loadFilterOptions().then(() => {
      this.loadMaterials()
    })
  },

  onShow() {
    // 页面显示时不自动刷新，避免用户体验问题
    // 只在首次加载或用户主动刷新时加载数据
  },

  onReachBottom() {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadMaterials()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData()
  },

  onUnload() {
    // 页面卸载时保存筛选条件
    this.saveFilterMemory()
  },

  // 保存筛选条件记忆
  saveFilterMemory() {
    const filterMemory = {
      selectedGrade: this.data.selectedGrade,
      selectedSubject: this.data.selectedSubject,
      selectedVolume: this.data.selectedVolume,
      selectedCategory: this.data.selectedCategory,
      sortType: this.data.sortType,
      sortOrder: this.data.sortOrder
    }
    wx.setStorageSync('categoryFilterMemory', filterMemory)
  },

  // 恢复筛选条件记忆
  restoreFilterMemory() {
    try {
      const filterMemory = wx.getStorageSync('categoryFilterMemory')
      if (filterMemory) {
        this.setData({
          selectedGrade: filterMemory.selectedGrade || '',
          selectedSubject: filterMemory.selectedSubject || '',
          selectedVolume: filterMemory.selectedVolume || '',
          selectedCategory: filterMemory.selectedCategory || '',
          sortType: filterMemory.sortType || 'download',
          sortOrder: filterMemory.sortOrder || 'desc'
        })
      }
    } catch (error) {
      console.log('恢复筛选条件失败:', error)
    }
  },

  // 加载筛选选项
  async loadFilterOptions() {
    try {
      // 调用新的云函数获取筛选选项
      const result = await wx.cloud.callFunction({
        name: 'material-getFilterOptions'
      })
      
      if (result.result && result.result.success) {
        const data = result.result.data
        
        // 处理云函数返回的对象数组，提取name字段
        const gradeOptions = (data.grades || []).map(item => typeof item === 'string' ? item : item.name)
        const subjectOptions = (data.subjects || []).map(item => typeof item === 'string' ? item : item.name)
        const volumeOptions = (data.volumes || []).map(item => typeof item === 'string' ? item : item.name) // 册别
        const categoryOptions = (data.sections || []).map(item => typeof item === 'string' ? item : item.name) // 板块
        
        this.setData({
          gradeOptions,
          subjectOptions,
          volumeOptions,
          categoryOptions
        })
      } else {
        console.error('获取分类选项失败:', result.result?.message)
        // 使用默认选项作为备选
        this.setDefaultOptions()
      }
    } catch (error) {
      console.error('加载筛选选项失败:', error)
      // 使用默认选项作为备选
      this.setDefaultOptions()
    }
  },

  // 设置默认筛选选项（备选方案）
  setDefaultOptions() {
    this.setData({
      gradeOptions: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
      subjectOptions: ['语文', '数学', '英语', '科学', '道德与法治'],
      volumeOptions: ['上册', '下册', '全册'],
      categoryOptions: ['单元同步', '单元知识点', '期中试卷', '期末试卷', '练习册', '课件资料']
    })
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      materialList: [],
      hasMore: true
    })
    // 刷新时也重新加载筛选选项
    this.loadFilterOptions().then(() => {
      this.loadMaterials().then(() => {
        wx.stopPullDownRefresh()
      }).catch(() => {
        wx.stopPullDownRefresh()
      })
    })
  },

  // 加载资料列表
  async loadMaterials() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      const params = {
        page: this.data.page,
        pageSize: 20,
        grade: this.data.selectedGrade,
        subject: this.data.selectedSubject,
        volume: this.data.selectedVolume,
        section: this.data.selectedCategory, // 映射到section字段
        sortType: this.data.sortType,
        sortOrder: this.data.sortOrder,
        excludeUpgrade: true  // 排除升学专区数据
      }
      
      console.log('调用云函数获取资料列表，参数:', params)
      
      // 直接调用云函数
      const result = await wx.cloud.callFunction({
        name: 'material-getList',
        data: params
      })
      
      console.log('云函数返回结果:', result)
      
      if (result.result && result.result.success) {
        const newList = this.data.page === 1 ? result.result.data : [...this.data.materialList, ...result.result.data]
        
        this.setData({
          materialList: newList,
          hasMore: result.result.hasMore,
          page: this.data.page + 1
        })
      } else {
        console.error('获取资料列表失败:', result.result?.message)
        this.showRetryOption(result.result?.message || '加载失败')
        
        // 显示加载失败状态
        wx.showToast({
          title: '暂无数据',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载资料列表失败:', error)
      this.showRetryOption('网络错误')
      
      // 显示加载失败状态
      wx.showToast({
        title: '暂无数据',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 显示重试选项
  showRetryOption(message) {
    wx.showModal({
      title: '加载失败',
      content: `${message}，是否重试？`,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          this.loadMaterials()
        }
      }
    })
  },


  // 筛选器相关方法
  showGradeFilter() {
    this.setData({ showGradePopup: true })
  },

  showSubjectFilter() {
    this.setData({ showSubjectPopup: true })
  },

  showVolumeFilter() {
    this.setData({ showVolumePopup: true })
  },

  showCategoryFilter() {
    this.setData({ showCategoryPopup: true })
  },

  hideGradeFilter() {
    this.setData({ showGradePopup: false })
  },

  hideSubjectFilter() {
    this.setData({ showSubjectPopup: false })
  },

  hideVolumeFilter() {
    this.setData({ showVolumePopup: false })
  },

  hideCategoryFilter() {
    this.setData({ showCategoryPopup: false })
  },

  // 选择筛选条件
  selectGrade(e) {
    const grade = e.currentTarget.dataset.grade
    this.setData({
      selectedGrade: grade,
      showGradePopup: false,
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  selectSubject(e) {
    const subject = e.currentTarget.dataset.subject
    this.setData({
      selectedSubject: subject,
      showSubjectPopup: false,
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  selectVolume(e) {
    const volume = e.currentTarget.dataset.volume
    this.setData({
      selectedVolume: volume,
      showVolumePopup: false,
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      selectedCategory: category,
      showCategoryPopup: false,
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  // 清除筛选条件
  clearGrade() {
    this.setData({
      selectedGrade: '',
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  clearSubject() {
    this.setData({
      selectedSubject: '',
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  clearVolume() {
    this.setData({
      selectedVolume: '',
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  clearCategory() {
    this.setData({
      selectedCategory: '',
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  // 清除所有筛选条件
  clearAllFilters() {
    this.setData({
      selectedGrade: '',
      selectedSubject: '',
      selectedVolume: '',
      selectedCategory: '',
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  // 排序相关方法 - 智能切换排序
  setSortType(e) {
    const sortType = e.currentTarget.dataset.type
    
    // 如果点击的是当前排序类型，则切换排序方向
    if (this.data.sortType === sortType) {
      const sortOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
      this.setData({
        sortOrder: sortOrder,
        page: 1,
        materialList: [],
        hasMore: true
      })
      
      // 显示排序切换提示
      wx.showToast({
        title: `按${this.getSortTypeName(sortType)}${sortOrder === 'desc' ? '降序' : '升序'}排列`,
        icon: 'none',
        duration: 1500
      })
    } else {
      // 如果是新的排序类型，默认使用降序
      this.setData({
        sortType: sortType,
        sortOrder: 'desc',
        page: 1,
        materialList: [],
        hasMore: true
      })
      
      wx.showToast({
        title: `按${this.getSortTypeName(sortType)}排序`,
        icon: 'none',
        duration: 1500
      })
    }
    
    this.saveFilterMemory()
    this.loadMaterials()
  },

  // 获取排序类型名称
  getSortTypeName(sortType) {
    const names = {
      'download': '下载量',
      'time': '时间',
      'points': '积分'
    }
    return names[sortType] || '默认'
  },

  changeSortType(e) {
    this.setSortType(e)
  },

  toggleSortOrder() {
    const sortOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    this.setData({
      sortOrder: sortOrder,
      page: 1,
      materialList: [],
      hasMore: true
    })
    this.saveFilterMemory()
    this.loadMaterials()
  },

  // 跳转到资料详情
  goToMaterialDetail(e) {
    const materialId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${materialId}`
    })
  },

  // WXML中使用的方法名
  goToDetail(e) {
    this.goToMaterialDetail(e)
  },

  // 重置筛选条件（WXML中使用的方法名）
  resetFilters() {
    this.clearAllFilters()
  },

  // 预览资料
  previewMaterial(e) {
    e.stopPropagation() // 阻止事件冒泡
    const material = e.currentTarget.dataset.material
    
    if (!material || !material.id) {
      wx.showToast({
        title: '资料信息错误',
        icon: 'none'
      })
      return
    }
    
    // 跳转到资料详情页面进行预览
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${material.id}&preview=true`
    })
  },

  // 下载资料
  async downloadMaterial(e) {
    e.stopPropagation() // 阻止事件冒泡
    const material = e.currentTarget.dataset.material
    
    if (!material || !material.id) {
      wx.showToast({
        title: '资料信息错误',
        icon: 'none'
      })
      return
    }
    
    try {
      // 检查用户积分是否足够
      const userPoints = app.getUserPoints()
      if (userPoints < material.points) {
        wx.showModal({
          title: '积分不足',
          content: `下载需要${material.points}积分，您当前有${userPoints}积分`,
          showCancel: true,
          confirmText: '去赚积分',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/earn-points/earn-points'
              })
            }
          }
        })
        return
      }
      
      // 显示下载确认
      wx.showModal({
        title: '确认下载',
        content: `下载"${material.title}"需要消耗${material.points}积分`,
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({
              title: '下载中...'
            })
            
            try {
              // 扣除积分
              app.updateUserPoints(-material.points)
              
              // 添加到下载记录
              app.addDownloadRecord(material)
              
              // 更新资料下载次数
              material.downloadCount = (material.downloadCount || 0) + 1
              
              wx.hideLoading()
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              })
              
              // 跳转到我的下载页面
              setTimeout(() => {
                wx.navigateTo({
                  url: '/pages/my-downloads/my-downloads'
                })
              }, 1500)
              
            } catch (error) {
              wx.hideLoading()
              wx.showToast({
                title: '下载失败',
                icon: 'none'
              })
            }
          }
        }
      })
      
    } catch (error) {
      console.error('下载资料失败:', error)
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  },

  // 收藏资料
  favoriteMaterial(e) {
    e.stopPropagation() // 阻止事件冒泡
    const material = e.currentTarget.dataset.material
    
    if (!material || !material.id) {
      wx.showToast({
        title: '资料信息错误',
        icon: 'none'
      })
      return
    }
    
    try {
      // 获取当前收藏列表
      const favorites = wx.getStorageSync('favorites') || []
      const isAlreadyFavorited = favorites.some(item => item.id === material.id)
      
      if (isAlreadyFavorited) {
        // 取消收藏
        const newFavorites = favorites.filter(item => item.id !== material.id)
        wx.setStorageSync('favorites', newFavorites)
        
        wx.showToast({
          title: '已取消收藏',
          icon: 'none'
        })
      } else {
        // 添加收藏
        const favoriteItem = {
          ...material,
          favoriteTime: new Date().toISOString()
        }
        favorites.push(favoriteItem)
        wx.setStorageSync('favorites', favorites)
        
        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        })
      }
      
      // 更新页面数据中的收藏状态
      const materialList = this.data.materialList.map(item => {
        if (item.id === material.id) {
          return {
            ...item,
            isFavorited: !isAlreadyFavorited
          }
        }
        return item
      })
      
      this.setData({ materialList })
      
    } catch (error) {
      console.error('收藏操作失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  }
})