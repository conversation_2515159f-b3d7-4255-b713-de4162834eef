import { Point } from './geo/point';
import { CollectionReference } from './collection';
import { Command } from './command';
interface GeoTeyp {
    Point: typeof Point;
}
export { Query } from './query';
export { CollectionReference } from './collection';
export { DocumentReference } from './document';
export declare class Db {
    Geo: GeoTeyp;
    command: typeof Command;
    RegExp: any;
    serverDate: any;
    startTransaction: any;
    runTransaction: any;
    logicCommand: any;
    updateCommand: any;
    queryCommand: any;
    config: any;
    static ws: any;
    static reqClass: any;
    static wsClass: any;
    static createSign: Function;
    static getAccessToken: Function;
    static dataVersion: string;
    static runtime: string;
    static appSecretInfo: any;
    constructor(config?: any);
    collection(collName: string): CollectionReference;
    createCollection(collName: string): any;
}
