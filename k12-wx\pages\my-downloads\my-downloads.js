// pages/my-downloads/my-downloads.js
const app = getApp()

Page({
  data: {
    downloads: [],
    loading: false,
    isEmpty: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    sortType: 'time', // time: 按时间排序, name: 按名称排序
    filterGrade: '', // 年级筛选
    filterSubject: '', // 学科筛选
    grades: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三'],
    subjects: ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '科学', '道德与法治']
  },

  onLoad() {
    this.loadDownloads()
  },

  onShow() {
    this.loadDownloads()
  },

  // 加载下载记录
  async loadDownloads(refresh = false) {
    if (this.data.loading) return
    
    // 如果是刷新，重置分页
    if (refresh) {
      this.setData({
        page: 1,
        downloads: [],
        hasMore: true
      })
    }
    
    this.setData({ loading: true })
    
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      const result = await app.api.download.getDownloadList({
        userId: userInfo._id,
        page: this.data.page,
        pageSize: this.data.pageSize,
        grade: this.data.filterGrade,
        subject: this.data.filterSubject
      })
      
      let newDownloads = refresh ? result.downloads : [...this.data.downloads, ...result.downloads]
      
      // 格式化下载记录数据
      newDownloads = newDownloads.map(item => ({
        id: item.materialId,
        _id: item._id,
        materialId: item.materialId,
        title: item.material.title,
        cover: item.material.cover,
        type: item.material.type,
        size: item.material.size,
        grade: item.material.grade,
        subject: item.material.subject,
        volume: item.material.volume,
        section: item.material.section,
        downloadTime: this.formatTime(item.downloadTime),
        downloadPath: item.downloadPath
      }))
      
      this.setData({
        downloads: newDownloads,
        isEmpty: newDownloads.length === 0,
        hasMore: result.hasMore,
        page: refresh ? 2 : this.data.page + 1,
        loading: false
      })
      
    } catch (error) {
      console.error('加载下载记录失败:', error)
      
      // 使用本地存储作为降级处理
      this.loadLocalDownloads()
      
      wx.showToast({
        title: '加载失败，显示本地数据',
        icon: 'none'
      })
    }
  },

  // 本地存储降级处理
  loadLocalDownloads() {
    try {
      const downloads = wx.getStorageSync('downloads') || []
      downloads.sort((a, b) => new Date(b.downloadTime) - new Date(a.downloadTime))
      
      this.setData({
        downloads: downloads,
        isEmpty: downloads.length === 0,
        loading: false,
        hasMore: false
      })
    } catch (error) {
      console.error('加载本地下载记录失败:', error)
      this.setData({
        loading: false,
        isEmpty: true
      })
    }
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 排序切换
  onSortChange(e) {
    const sortType = e.detail.value
    this.setData({ sortType })
    this.sortDownloads(sortType)
  },

  // 排序下载记录
  sortDownloads(sortType) {
    let downloads = [...this.data.downloads]
    
    if (sortType === 'time') {
      downloads.sort((a, b) => new Date(b.downloadTime) - new Date(a.downloadTime))
    } else if (sortType === 'name') {
      downloads.sort((a, b) => a.title.localeCompare(b.title))
    }
    
    this.setData({ downloads })
  },

  // 年级筛选
  onGradeFilter() {
    const grades = ['全部', ...this.data.grades]
    
    wx.showActionSheet({
      itemList: grades,
      success: (res) => {
        const selectedGrade = res.tapIndex === 0 ? '' : grades[res.tapIndex]
        this.setData({ filterGrade: selectedGrade })
        this.filterDownloads()
      }
    })
  },

  // 学科筛选
  onSubjectFilter() {
    const subjects = ['全部', ...this.data.subjects]
    
    wx.showActionSheet({
      itemList: subjects,
      success: (res) => {
        const selectedSubject = res.tapIndex === 0 ? '' : subjects[res.tapIndex]
        this.setData({ filterSubject: selectedSubject })
        this.filterDownloads()
      }
    })
  },

  // 筛选下载记录
  filterDownloads() {
    // 重新加载数据，云函数会处理筛选
    this.loadDownloads(true)
  },

  // 清空筛选
  clearFilter() {
    this.setData({
      filterGrade: '',
      filterSubject: ''
    })
    this.loadDownloads(true)
  },

  // 查看资料详情
  viewMaterial(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 重新下载
  redownload(e) {
    const { item } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认下载',
      content: `确定要重新下载"${item.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDownload(item)
        }
      }
    })
  },

  // 执行下载
  async performDownload(item) {
    wx.showLoading({ title: '下载中...' })
    
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      // 调用云函数更新下载记录
      await app.api.download.addDownload(item.materialId || item.id, item.downloadPath)
      
      // 重新加载下载列表
      this.loadDownloads(true)
      
      wx.hideLoading()
      wx.showToast({
        title: '下载成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('下载失败:', error)
      wx.hideLoading()
      
      // 降级到本地存储处理
      try {
        let downloads = wx.getStorageSync('downloads') || []
        const index = downloads.findIndex(d => d.id === item.id)
        if (index !== -1) {
          downloads[index].downloadTime = new Date().toISOString()
          wx.setStorageSync('downloads', downloads)
          this.loadLocalDownloads()
        }
        
        wx.showToast({
          title: '下载成功',
          icon: 'success'
        })
      } catch (localError) {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    }
  },

  // 删除下载记录
  deleteDownload(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条下载记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(id)
        }
      }
    })
  },

  // 执行删除
  async performDelete(id) {
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      // 找到对应的下载记录
      const downloadItem = this.data.downloads.find(item => 
        item.id === id || item._id === id || item.materialId === id
      )
      
      if (downloadItem) {
        // 调用云函数删除
        await app.api.download.removeDownload(downloadItem._id, downloadItem.materialId)
        
        // 从本地列表中移除
        let downloads = this.data.downloads.filter(item => 
          item.id !== id && item._id !== id && item.materialId !== id
        )
        
        this.setData({
          downloads: downloads,
          isEmpty: downloads.length === 0
        })
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        throw new Error('未找到下载记录')
      }
      
    } catch (error) {
      console.error('删除失败:', error)
      
      // 降级到本地存储处理
      try {
        let downloads = wx.getStorageSync('downloads') || []
        downloads = downloads.filter(item => item.id !== id)
        wx.setStorageSync('downloads', downloads)
        
        let localDownloads = this.data.downloads.filter(item => 
          item.id !== id && item._id !== id
        )
        
        this.setData({
          downloads: localDownloads,
          isEmpty: localDownloads.length === 0
        })
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } catch (localError) {
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    }
  },

  // 清空所有下载记录
  clearAll() {
    if (this.data.downloads.length === 0) return
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有下载记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.performClearAll()
        }
      }
    })
  },

  // 执行清空所有记录
  async performClearAll() {
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      // 批量删除所有下载记录
      const materialIds = this.data.downloads.map(item => item.materialId || item.id)
      
      for (const materialId of materialIds) {
        await app.api.download.removeDownload(null, materialId)
      }
      
      this.setData({
        downloads: [],
        isEmpty: true
      })
      
      wx.showToast({
        title: '清空成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('清空失败:', error)
      
      // 降级到本地存储处理
      try {
        wx.removeStorageSync('downloads')
        this.setData({
          downloads: [],
          isEmpty: true
        })
        
        wx.showToast({
          title: '清空成功',
          icon: 'success'
        })
      } catch (localError) {
        wx.showToast({
          title: '清空失败',
          icon: 'none'
        })
      }
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDownloads(true)
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadDownloads()
    }
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的下载记录 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})