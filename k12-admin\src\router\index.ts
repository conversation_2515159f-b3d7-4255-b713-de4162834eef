import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '../views/Layout.vue'
import Dashboard from '../views/Dashboard.vue'
import FileManage from '../views/FileManage.vue'
import CategoryManage from '../views/CategoryManage.vue'
import UserManage from '../views/UserManage.vue'
import SystemConfig from '../views/SystemConfig.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: Dashboard
      },
      {
        path: '/files',
        name: 'FileManage',
        component: FileManage
      },
      {
        path: '/categories',
        name: 'CategoryManage',
        component: CategoryManage
      },
      {
        path: '/users',
        name: 'UserManage',
        component: UserManage
      },
      {
        path: '/settings',
        name: 'SystemConfig',
        component: SystemConfig
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
