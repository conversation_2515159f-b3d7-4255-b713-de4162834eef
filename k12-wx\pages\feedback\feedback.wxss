/* pages/feedback/feedback.wxss */
.container {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 40rpx;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 32rpx 20rpx;
  background: linear-gradient(135deg, #1677FF 0%, #4096FF 100%);
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 反馈表单 */
.feedback-form {
  padding: 32rpx;
}

.form-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.required {
  color: #FF4D4F;
  font-size: 28rpx;
}

.optional {
  color: #666666;
  font-size: 24rpx;
}

/* 选择器 */
.picker-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1rpx solid #E6E6E6;
}

.picker-text {
  font-size: 30rpx;
  color: #333333;
}

.picker-arrow {
  color: #999999;
  font-size: 24rpx;
}

/* 输入框 */
.input-field {
  width: 100%;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1rpx solid #E6E6E6;
  font-size: 30rpx;
  color: #333333;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #1677FF;
}

.textarea-field {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background-color: white;
  border-radius: 12rpx;
  border: 1rpx solid #E6E6E6;
  font-size: 30rpx;
  color: #333333;
  box-sizing: border-box;
}

.textarea-field:focus {
  border-color: #1677FF;
}

.input-counter {
  text-align: right;
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

.input-hint {
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
}

/* 图片上传 */
.image-upload {
  background-color: white;
  border-radius: 12rpx;
  padding: 24rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #FF4D4F;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.image-add {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #E6E6E6;
  border-radius: 8rpx;
  color: #999999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #666666;
  margin-top: 16rpx;
}

/* 操作按钮 */
.action-buttons {
  padding: 0 32rpx;
  margin-top: 48rpx;
}

.button-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.secondary-button {
  flex: 1;
  padding: 24rpx;
  background-color: white;
  color: #1677FF;
  border: 1rpx solid #1677FF;
  border-radius: 12rpx;
  text-align: center;
  font-size: 30rpx;
}

.secondary-button:active {
  background-color: #FFFFFF;
}

.primary-button {
  width: 100%;
  padding: 24rpx;
  background-color: #1677FF;
  color: white;
  border-radius: 12rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
}

.primary-button:active {
  background-color: #0958D9;
}

.primary-button.disabled {
  background-color: #999999;
  color: white;
}

/* 提示信息 */
.tips-section {
  margin-top: 48rpx;
  padding: 32rpx;
  background-color: white;
  border-radius: 12rpx;
  margin: 48rpx 32rpx 0;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.tips-list {
  padding-left: 16rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}
