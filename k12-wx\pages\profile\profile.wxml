<view class="container">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <!-- 未登录状态 -->
    <view class="user-info" bindtap="goToLogin" wx:if="{{!userInfo.isLogin}}">
      <view class="avatar-wrapper">
        <text class="avatar-icon">👤</text>
      </view>
      <view class="user-details">
        <view class="username">点击登录</view>
        <view class="user-desc">登录后享受更多功能</view>
      </view>
      <text class="arrow-icon">›</text>
    </view>
    
    <!-- 已登录状态 -->
    <view class="user-info" wx:if="{{userInfo.isLogin}}">
      <view class="avatar-wrapper">
        <image class="avatar" src="{{userInfo.avatar}}" mode="aspectFill" wx:if="{{userInfo.avatar}}"></image>
        <text class="avatar-icon" wx:else>👤</text>
      </view>
      <view class="user-details">
        <view class="username">{{userInfo.nickname || '用户'}}</view>
        <view class="user-desc">已加入{{userInfo.joinDays || 0}}天</view>
      </view>
      <view class="user-points">
        <text class="points-value">{{userInfo.points || 0}}</text>
        <text class="points-label">积分</text>
      </view>
    </view>
  </view>

  <!-- 用户统计数据 -->
  <view class="stats-section" wx:if="{{userInfo.isLogin}}">
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-value">{{userStats.downloadCount || 0}}</view>
        <view class="stats-label">下载数</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{userStats.favoriteCount || 0}}</view>
        <view class="stats-label">收藏数</view>
      </view>
      <view class="stats-item">
        <view class="stats-value">{{userStats.shareCount || 0}}</view>
        <view class="stats-label">分享次数</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="goToDownloads">
      <view class="menu-icon">📥</view>
      <text class="menu-text">我的下载</text>
      <text class="arrow-icon">›</text>
    </view>
    
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-icon">⭐</view>
      <text class="menu-text">我的收藏</text>
      <text class="arrow-icon">›</text>
    </view>
    
    <view class="menu-item" bindtap="goToEarnPoints">
      <view class="menu-icon">💰</view>
      <text class="menu-text">去赚积分</text>
      <view class="menu-badge">赚积分</view>
      <text class="arrow-icon">›</text>
    </view>
    
    <view class="menu-item" bindtap="goToPointsDetail">
      <view class="menu-icon">📊</view>
      <text class="menu-text">积分明细</text>
      <text class="arrow-icon">›</text>
    </view>
  </view>

  <!-- 设置区域 -->
  <view class="settings-section">
    <view class="menu-item" bindtap="goToFeedback">
      <view class="menu-icon">💬</view>
      <text class="menu-text">意见反馈</text>
      <text class="arrow-icon">›</text>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-icon">ℹ️</view>
      <text class="menu-text">关于我们</text>
      <text class="arrow-icon">›</text>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo.isLogin}}">
    <view class="logout-btn" bindtap="logout">退出登录</view>
  </view>
</view>
