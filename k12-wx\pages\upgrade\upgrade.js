// 升学专区页面逻辑
const app = getApp()

Page({
  data: {
    // 页面信息
    pageTitle: '',
    pageSubtitle: '',
    upgradeType: '', // kindergarten 或 primary
    
    // 筛选标签
    filterTabs: [],
    activeTab: 'all',
    
    // 资料列表
    materialList: [],
    
    // 加载状态
    loading: false
  },

  onLoad(options) {
    const type = options.type || 'kindergarten'
    this.setData({ upgradeType: type })
    this.initPageInfo(type)
    this.initFilterTabs(type)  // 初始化分类标签
    this.loadMaterials()
  },

  // 初始化页面信息
  initPageInfo(type) {
    const pageInfo = {
      kindergarten: {
        title: '幼升小',
        subtitle: '为孩子入学做好充分准备'
      },
      primary: {
        title: '小升初',
        subtitle: '助力孩子顺利升入理想中学'
      }
    }

    const info = pageInfo[type] || pageInfo.kindergarten
    this.setData({
      pageTitle: info.title,
      pageSubtitle: info.subtitle
    })
  },

  // 初始化筛选标签 - 直接使用中文分类名
  initFilterTabs(type) {
    // 固定的分类配置 - 直接使用中文名称
    const fixedTabs = {
      kindergarten: [
        { key: 'all', name: '全部' },
        { key: '拼音启蒙', name: '拼音启蒙' },
        { key: '认识数字', name: '认识数字' },
        { key: '习惯养成', name: '习惯养成' },
        { key: '学科启蒙', name: '学科启蒙' },
        { key: '知识科普', name: '知识科普' }
      ],
      primary: [
        { key: 'all', name: '全部' },
        { key: '真题模拟', name: '真题模拟' },
        { key: '语文冲刺', name: '语文冲刺' },
        { key: '数学冲刺', name: '数学冲刺' },
        { key: '英语强化', name: '英语强化' },
        { key: '面试准备', name: '面试准备' }
      ]
    }
    
    const tabs = fixedTabs[type] || fixedTabs.kindergarten
    console.log('固定分类标签:', tabs)
    
    this.setData({
      filterTabs: tabs
    })
  },

  // 切换筛选标签
  switchTab(e) {
    const key = e.currentTarget.dataset.key
    this.setData({ activeTab: key })
    this.loadMaterials()
  },

  // 加载资料列表
  async loadMaterials() {
    this.setData({ loading: true })
    
    try {
      console.log('开始加载升学资料...', {
        upgradeType: this.data.upgradeType,
        activeTab: this.data.activeTab
      })
      
      // 构建查询条件
      const queryData = {
        pageSize: 20,
        sortBy: 'downloadCount'
      }
      
      // 根据升学类型和筛选标签添加查询条件
      if (this.data.upgradeType === 'kindergarten') {
        queryData.category = '幼升小'
      } else if (this.data.upgradeType === 'primary') {
        queryData.category = '小升初'
      }
      
      // 直接查询files集合获取资料列表
      const db = wx.cloud.database()
      const _ = db.command
      
      // 构建查询条件 - 修正字段映射
      const typeMapping = {
        'kindergarten': '幼升小',
        'primary': '小升初'
      }
      
      let whereCondition = {
        status: 'active',  // 添加状态过滤
        upgradeType: typeMapping[this.data.upgradeType] || '幼升小'
      }
      
      // 根据筛选标签进一步过滤 - 直接使用中文分类名
      if (this.data.activeTab !== 'all') {
        whereCondition.upgradeCategory = this.data.activeTab
      }
      
      console.log('查询条件:', whereCondition)
      
      const res = await db.collection('files')
        .where(whereCondition)
        .orderBy('download_count', 'desc')
        .limit(20)
        .get()
      
      console.log('数据库查询结果:', res)
      
      if (res.data) {
        const materials = res.data || []
        console.log('获取到的升学资料:', materials)
        
        // 转换字段名格式，将数据库的下划线格式转换为前端期望的驼峰格式
        const formattedMaterials = materials.map(item => ({
          ...item,
          downloadCount: item.download_count || 0,
          viewCount: item.view_count || 0,
          points: item.points || 0
        }))
        
        this.setData({
          materialList: formattedMaterials
        })
      } else {
        throw new Error('获取数据失败')
      }
      
    } catch (error) {
      console.error('加载升学资料失败:', error)
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
      
      // 设置空列表，不使用模拟数据
      this.setData({
        materialList: []
      })
    } finally {
      this.setData({ loading: false })
    }
  },


  // 跳转到资料详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadMaterials().then(() => {
      wx.stopPullDownRefresh()
    })
  }
})