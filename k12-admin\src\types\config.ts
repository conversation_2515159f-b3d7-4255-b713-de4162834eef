// 基础配置类型
export interface BasicConfig {
  miniProgramName: string
  miniProgramVersion: string
  adminEmail: string
  supportPhone: string
  miniProgramDescription: string
  recommendedContent: string
  miniProgramEnabled: boolean
  maintenanceMode: boolean
  maintenanceMessage: string
}

// 功能开关配置类型
export interface FeaturesConfig {
  watchAdEnabled: boolean
  shareEnabled: boolean
  favoriteEnabled: boolean
  registerEnabled: boolean
  commentEnabled: boolean
  searchEnabled: boolean
}

// 业务参数配置类型
export interface BusinessConfig {
  previewPages: number
  defaultPointPrice: number
  maxFileSize: number
  dailyPointLimit: number
  registerBonus: number
  shareBonus: number
  adBonus: number
  dailySignBonus: number
}

// 存储配置类型
export interface StorageConfig {
  storageType: 'cloud' | 'local' | 'third-party'
  maxStorageSize: string
  maxFileSize: string
  allowedFileTypes: string[]
  autoCleanup: boolean
  cleanupDays: string
}

// 开发环境配置类型
export interface SecurityConfig {
  debugMode: boolean
  consoleLog: boolean
  apiLog: boolean
  showErrorDetails: boolean
  devNotes: string
}

// 系统配置总类型
export interface SystemConfig {
  basic: BasicConfig
  features: FeaturesConfig
  business: BusinessConfig
  storage: StorageConfig
  security: SecurityConfig
}