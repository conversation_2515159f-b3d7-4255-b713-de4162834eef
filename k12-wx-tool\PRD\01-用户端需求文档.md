# K12教育资源小程序 - 产品需求文档

## 1. 产品概述

### 1.1 产品定位
K12教育资源小程序，专门为幼升小到六年级的家长提供各类教辅资料的查找和下载服务。通过积分制度让家长能够方便地获取孩子需要的学习资料，打造家长信赖的教育资源平台。

### 1.2 产品简介
这是一个基于微信小程序的K12教育资源平台，家长可以通过积分下载各种教辅资料，包括试卷、练习册、知识点总结等。用户可以通过分享、观看广告、每日签到等方式获取积分，然后用积分下载需要的资料。平台提供完善的用户管理功能，包括下载历史、收藏管理、积分明细等。

## 2. 主要用途和功能

### 2.1 核心功能

#### 2.1.1 资料下载服务
- **教辅资料库**：提供幼升小到六年级的各类教辅资料
- **积分下载**：用积分购买和下载资料
- **资料预览**：下载前可以预览资料的前几页
- **分类筛选**：按年级、科目、册别、板块进行筛选
#### 2.1.2 积分获取系统
| 获取方式 | 积分数量 | 限制条件 |
|---------|---------|---------|
| 分享获取积分 | 50积分 | 每日限制3次 |
| 观看广告获取积分 | 100积分 | 每日限制5次 |
| 邀请奖励 | 100积分 | 每日限制3次 |
| 每日签到 | 50积分 | 每日一次 |

> 默认新用户自动获取200积分。
> 每个积分功能都有独立的开关，开启后才可以用

#### 2.1.3 个人管理功能
- **我的下载**：查看已下载的资料历史，支持重新下载
- **我的收藏**：收藏感兴趣的资料，便于后续查找
- **积分管理**：查看积分余额、获取记录和消费明细
- **赚积分中心**：集中展示所有积分获取方式
- **用户反馈**：意见反馈和问题建议渠道
- **关于我们**：应用的相关介绍

### 2.2 目标用户

#### 2.2.1 主要用户
- **家长**：幼升小到六年级学生的家长
- **使用场景**：
  - 课后辅导需要练习题
  - 考试前需要复习资料
  - 假期需要预习或复习材料
  - 孩子薄弱环节需要专项练习

### 2.3 产品价值

#### 2.3.1 对家长的价值
- 方便快捷地找到适合孩子的教辅资料
- 通过积分制度降低获取资料的成本
- 资料预览功能避免下载不合适的内容
- 统一管理孩子的学习资料
- 完善的历史记录帮助追踪学习进度

## 3. 资料分类

### 3.1 分类

#### 3.1.1 年级
- 一年级、二年级、三年级、四年级、五年级、六年级
#### 3.1.2 专区
- 幼升小、小升初

### 3.2 科目分类
- 语文、数学、英语、科学等主要学科

### 3.3 册别分类
- 上册、下册、全册

### 3.4 板块分类
- 单元同步
- 单元知识点
- 核心知识点
- 期中期末试卷
- 专项练习

> 以上分类都是在后台上传文件的时候自定义写入

## 4. 技术实现特色

### 4.1 云开发架构
- 基于微信小程序云开发
- 云函数处理积分操作和历史记录
- 云数据库存储用户数据和资料信息
- 云存储管理教辅资料文件

### 4.2 积分系统
- 完善的积分获取和消费机制
- 积分历史详细记录
- 防刷机制保证系统安全

### 4.3 用户体验优化
- 四个Tab页面清晰导航
- 完整的用户行为追踪
- 多维度的资料管理功能

---