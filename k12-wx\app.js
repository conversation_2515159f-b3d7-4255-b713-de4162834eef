// app.js
const { api } = require('./utils/api.js')

App({
  globalData: {
    userInfo: null,
    userPoints: 0,
    systemInfo: null
  },

  // 将API挂载到全局
  api: api,

  onLaunch() {
    console.log('小程序启动')
    
    // 初始化云开发 - 必须先完成
    this.initCloud()
    
    // 延迟执行其他初始化，确保云环境初始化完成
    setTimeout(() => {
      // 获取系统信息
      this.getSystemInfo()
      
      // 初始化用户数据
      this.initUserData()
      
      // 检查更新
      this.checkForUpdate()
    }, 100)
  },

  // 初始化云开发
  initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return false
    } else {
      try {
        wx.cloud.init({
          // env 参数说明：
          //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
          //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
          //   如不填则使用默认环境（第一个创建的环境）
          env: 'cloud1-8gm001v7fd56ff43',
          traceUser: true,
        })
        console.log('云开发初始化成功')
        return true
      } catch (error) {
        console.error('云开发初始化失败:', error)
        return false
      }
    }
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(error) {
    console.error('小程序错误:', error)
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      wx.getSystemInfo({
        success: (res) => {
          this.globalData.systemInfo = res
          console.log('系统信息:', res)
        },
        fail: (error) => {
          console.error('获取系统信息失败:', error)
        }
      })
    } catch (error) {
      console.error('getSystemInfo调用异常:', error)
      // 设置默认系统信息
      this.globalData.systemInfo = {
        platform: 'devtools',
        version: '1.0.0'
      }
    }
  },

  // 初始化用户数据
  initUserData() {
    try {
      // 初始化用户积分
      const points = wx.getStorageSync('userPoints')
      if (!points) {
        wx.setStorageSync('userPoints', 0)
        this.globalData.userPoints = 0
      } else {
        this.globalData.userPoints = points
      }

      // 初始化用户信息
      const userInfo = wx.getStorageSync('userInfo')
      if (userInfo) {
        this.globalData.userInfo = userInfo
      }

      // 初始化其他数据
      if (!wx.getStorageSync('downloads')) {
        wx.setStorageSync('downloads', [])
      }
      if (!wx.getStorageSync('favorites')) {
        wx.setStorageSync('favorites', [])
      }
      if (!wx.getStorageSync('viewHistory')) {
        wx.setStorageSync('viewHistory', [])
      }
      if (!wx.getStorageSync('searchHistory')) {
        wx.setStorageSync('searchHistory', [])
      }
      if (!wx.getStorageSync('earnedPoints')) {
        wx.setStorageSync('earnedPoints', 0)
      }

    } catch (error) {
      console.error('初始化用户数据失败:', error)
    }
  },

  // 检查小程序更新
  checkForUpdate() {
    try {
      if (wx.canIUse && wx.canIUse('getUpdateManager')) {
        const updateManager = wx.getUpdateManager()

        updateManager.onCheckForUpdate((res) => {
          console.log('检查更新结果:', res.hasUpdate)
        })

        updateManager.onUpdateReady(() => {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否重启应用？',
            success: (res) => {
              if (res.confirm) {
                updateManager.applyUpdate()
              }
            }
          })
        })

        updateManager.onUpdateFailed(() => {
          console.error('新版本下载失败')
        })
      }
    } catch (error) {
      console.error('检查更新异常:', error)
      // 在开发工具中忽略更新检查错误
    }
  },

  // 获取用户积分
  getUserPoints() {
    try {
      const points = wx.getStorageSync('userPoints') || 0
      this.globalData.userPoints = points
      return points
    } catch (error) {
      console.error('获取用户积分失败:', error)
      return 0
    }
  },

  // 更新用户积分
  updateUserPoints(points) {
    try {
      wx.setStorageSync('userPoints', points)
      this.globalData.userPoints = points
      
      // 触发积分变化事件
      this.triggerPointsChange(points)
      
      return true
    } catch (error) {
      console.error('更新用户积分失败:', error)
      return false
    }
  },

  // 增加用户积分
  addUserPoints(points) {
    const currentPoints = this.getUserPoints()
    const newPoints = currentPoints + points
    return this.updateUserPoints(newPoints)
  },

  // 扣除用户积分
  deductUserPoints(points) {
    const currentPoints = this.getUserPoints()
    if (currentPoints < points) {
      return false // 积分不足
    }
    const newPoints = currentPoints - points
    return this.updateUserPoints(newPoints)
  },

  // 触发积分变化事件
  triggerPointsChange(points) {
    // 可以在这里添加积分变化的全局处理逻辑
    console.log('用户积分变化:', points)
  },

  // 获取用户信息
  getUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      this.globalData.userInfo = userInfo
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    try {
      wx.setStorageSync('userInfo', userInfo)
      this.globalData.userInfo = userInfo
      return true
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return false
    }
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = this.getUserInfo()
    return !!(userInfo && userInfo.openid)
  },

  // 添加浏览历史
  addViewHistory(material) {
    try {
      const history = wx.getStorageSync('viewHistory') || []
      
      // 移除重复项
      const filteredHistory = history.filter(item => item.id !== material.id)
      
      // 添加到开头
      filteredHistory.unshift({
        ...material,
        viewTime: new Date().getTime()
      })
      
      // 限制数量（最多保存100条）
      const limitedHistory = filteredHistory.slice(0, 100)
      
      wx.setStorageSync('viewHistory', limitedHistory)
      return true
    } catch (error) {
      console.error('添加浏览历史失败:', error)
      return false
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    
    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前'
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前'
    } else if (diff < week) {
      return Math.floor(diff / day) + '天前'
    } else if (diff < month) {
      return Math.floor(diff / week) + '周前'
    } else {
      return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate()
    }
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 防抖函数
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle
    return function() {
      const args = arguments
      const context = this
      if (!inThrottle) {
        func.apply(context, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // 显示加载提示
  showLoading(title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    })
  },

  // 隐藏加载提示
  hideLoading() {
    wx.hideLoading()
  },

  // 显示成功提示
  showSuccess(title = '操作成功') {
    wx.showToast({
      title: title,
      icon: 'success',
      duration: 2000
    })
  },

  // 显示错误提示
  showError(title = '操作失败') {
    wx.showToast({
      title: title,
      icon: 'none',
      duration: 2000
    })
  },

  // 确认对话框
  showConfirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title: title,
        content: content,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }
})