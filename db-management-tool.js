// 云数据库管理工具
const tcb = require('tcb-admin-node');

// 云环境配置
const ENV_ID = 'cloud1-8gm001v7fd56ff43';
const APP_ID = 'wxdcb01784f343322b';

// 初始化云开发
const app = tcb.init({
  env: ENV_ID
});

const db = app.database();
const _ = db.command;

class DatabaseManager {
  constructor() {
    this.collections = {
      users: db.collection('users'),
      files: db.collection('files'),
      point_records: db.collection('point_records'),
      favorites: db.collection('favorites'),
      downloads: db.collection('downloads'),
      shares: db.collection('shares'),
      test_logs: db.collection('test_logs')
    };
  }

  // 获取集合统计信息
  async getCollectionStats() {
    console.log('📊 获取数据库集合统计信息...');
    const stats = {};
    
    for (const [name, collection] of Object.entries(this.collections)) {
      try {
        const count = await collection.count();
        stats[name] = count.total;
        console.log(`   ${name}: ${count.total} 条记录`);
      } catch (error) {
        stats[name] = `错误: ${error.message}`;
        console.log(`   ${name}: 获取失败 - ${error.message}`);
      }
    }
    
    return stats;
  }

  // 清空指定集合
  async clearCollection(collectionName) {
    if (!this.collections[collectionName]) {
      throw new Error(`集合 ${collectionName} 不存在`);
    }

    console.log(`🗑️  开始清空集合: ${collectionName}`);
    
    const collection = this.collections[collectionName];
    
    // 分批删除，避免一次性删除太多数据
    let totalDeleted = 0;
    const batchSize = 100;
    
    while (true) {
      const query = await collection.limit(batchSize).get();
      
      if (query.data.length === 0) {
        break;
      }
      
      // 批量删除
      const deletePromises = query.data.map(doc => 
        collection.doc(doc._id).remove()
      );
      
      await Promise.all(deletePromises);
      totalDeleted += query.data.length;
      
      console.log(`   已删除 ${totalDeleted} 条记录...`);
    }
    
    console.log(`✅ 集合 ${collectionName} 清空完成，共删除 ${totalDeleted} 条记录`);
    return totalDeleted;
  }

  // 删除指定用户的所有数据
  async deleteUserData(openid) {
    console.log(`🗑️  开始删除用户数据: ${openid}`);
    
    const results = {};
    
    // 删除用户基本信息
    const userResult = await this.collections.users.where({ openid }).remove();
    results.users = userResult.deleted;
    console.log(`   删除用户记录: ${userResult.deleted} 条`);
    
    // 删除积分记录
    const pointResult = await this.collections.point_records.where({ user_id: openid }).remove();
    results.point_records = pointResult.deleted;
    console.log(`   删除积分记录: ${pointResult.deleted} 条`);
    
    // 删除收藏记录
    const favoriteResult = await this.collections.favorites.where({ user_id: openid }).remove();
    results.favorites = favoriteResult.deleted;
    console.log(`   删除收藏记录: ${favoriteResult.deleted} 条`);
    
    // 删除下载记录
    const downloadResult = await this.collections.downloads.where({ user_id: openid }).remove();
    results.downloads = downloadResult.deleted;
    console.log(`   删除下载记录: ${downloadResult.deleted} 条`);
    
    // 删除分享记录
    const shareResult = await this.collections.shares.where({ user_id: openid }).remove();
    results.shares = shareResult.deleted;
    console.log(`   删除分享记录: ${shareResult.deleted} 条`);
    
    console.log(`✅ 用户 ${openid} 的所有数据删除完成`);
    return results;
  }

  // 删除指定时间范围的数据
  async deleteDataByTimeRange(collectionName, startTime, endTime, timeField = 'created_time') {
    if (!this.collections[collectionName]) {
      throw new Error(`集合 ${collectionName} 不存在`);
    }

    console.log(`🗑️  删除 ${collectionName} 集合中 ${startTime} 到 ${endTime} 的数据...`);
    
    const collection = this.collections[collectionName];
    const whereCondition = {};
    whereCondition[timeField] = _.gte(new Date(startTime)).and(_.lte(new Date(endTime)));
    
    const result = await collection.where(whereCondition).remove();
    
    console.log(`✅ 删除完成，共删除 ${result.deleted} 条记录`);
    return result.deleted;
  }

  // 删除测试数据
  async deleteTestData() {
    console.log('🧪 开始删除测试数据...');
    
    const results = {};
    
    // 删除测试用户
    const testUserResult = await this.collections.users.where({
      openid: _.in(['test_user_123456', 'test_openid'])
    }).remove();
    results.test_users = testUserResult.deleted;
    console.log(`   删除测试用户: ${testUserResult.deleted} 条`);
    
    // 删除测试日志
    const testLogResult = await this.collections.test_logs.remove();
    results.test_logs = testLogResult.deleted;
    console.log(`   删除测试日志: ${testLogResult.deleted} 条`);
    
    // 删除包含"test"的文件
    const testFileResult = await this.collections.files.where({
      title: db.RegExp({
        regexp: 'test',
        options: 'i'
      })
    }).remove();
    results.test_files = testFileResult.deleted;
    console.log(`   删除测试文件: ${testFileResult.deleted} 条`);
    
    console.log('✅ 测试数据删除完成');
    return results;
  }

  // 备份集合数据
  async backupCollection(collectionName) {
    if (!this.collections[collectionName]) {
      throw new Error(`集合 ${collectionName} 不存在`);
    }

    console.log(`💾 开始备份集合: ${collectionName}`);
    
    const collection = this.collections[collectionName];
    const allData = [];
    
    // 分批获取所有数据
    let skip = 0;
    const limit = 100;
    
    while (true) {
      const query = await collection.skip(skip).limit(limit).get();
      
      if (query.data.length === 0) {
        break;
      }
      
      allData.push(...query.data);
      skip += limit;
      
      console.log(`   已备份 ${allData.length} 条记录...`);
    }
    
    // 保存到文件
    const fs = require('fs');
    const backupFileName = `backup_${collectionName}_${Date.now()}.json`;
    fs.writeFileSync(backupFileName, JSON.stringify(allData, null, 2));
    
    console.log(`✅ 备份完成，文件: ${backupFileName}，共 ${allData.length} 条记录`);
    return { fileName: backupFileName, count: allData.length };
  }

  // 查询数据
  async queryData(collectionName, whereCondition = {}, limit = 10) {
    if (!this.collections[collectionName]) {
      throw new Error(`集合 ${collectionName} 不存在`);
    }

    console.log(`🔍 查询集合 ${collectionName} 的数据...`);
    
    const collection = this.collections[collectionName];
    const query = await collection.where(whereCondition).limit(limit).get();
    
    console.log(`   查询结果: ${query.data.length} 条记录`);
    return query.data;
  }

  // 批量更新数据
  async batchUpdate(collectionName, whereCondition, updateData) {
    if (!this.collections[collectionName]) {
      throw new Error(`集合 ${collectionName} 不存在`);
    }

    console.log(`🔄 批量更新集合 ${collectionName} 的数据...`);
    
    const collection = this.collections[collectionName];
    const result = await collection.where(whereCondition).update({
      data: updateData
    });
    
    console.log(`✅ 更新完成，共更新 ${result.updated} 条记录`);
    return result.updated;
  }
}

// 命令行工具
async function runCommand() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const dbManager = new DatabaseManager();
  
  try {
    switch (command) {
      case 'stats':
        await dbManager.getCollectionStats();
        break;
        
      case 'clear':
        const collectionName = args[1];
        if (!collectionName) {
          console.log('用法: node db-management-tool.js clear <集合名>');
          return;
        }
        await dbManager.clearCollection(collectionName);
        break;
        
      case 'delete-user':
        const openid = args[1];
        if (!openid) {
          console.log('用法: node db-management-tool.js delete-user <openid>');
          return;
        }
        await dbManager.deleteUserData(openid);
        break;
        
      case 'delete-test':
        await dbManager.deleteTestData();
        break;
        
      case 'backup':
        const backupCollection = args[1];
        if (!backupCollection) {
          console.log('用法: node db-management-tool.js backup <集合名>');
          return;
        }
        await dbManager.backupCollection(backupCollection);
        break;
        
      default:
        console.log('可用命令:');
        console.log('  stats                    - 显示所有集合统计信息');
        console.log('  clear <集合名>           - 清空指定集合');
        console.log('  delete-user <openid>     - 删除指定用户的所有数据');
        console.log('  delete-test              - 删除所有测试数据');
        console.log('  backup <集合名>          - 备份指定集合');
    }
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCommand();
}

module.exports = DatabaseManager;
