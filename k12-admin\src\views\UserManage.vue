<template>
  <div class="user-manage">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h1>用户管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="danger" :disabled="selectedUsers.length === 0" @click="batchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ userStats.total }}</div>
              <div class="stat-label">总用户数</div>
            </div>
            <el-icon class="stat-icon total"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ userStats.active }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <el-icon class="stat-icon active"><UserFilled /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ userStats.banned }}</div>
              <div class="stat-label">封禁用户</div>
            </div>
            <el-icon class="stat-icon banned"><Lock /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户昵称">
          <el-input
            v-model="searchForm.nickname"
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="用户状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="正常" value="active" />
            <el-option label="封禁" value="banned" />
            <el-option label="未激活" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><RefreshRight /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :src="row.avatar_url" :alt="row.nickname" />
          </template>
        </el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="120" />
        <el-table-column prop="openid" label="OpenID" width="200" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="积分" width="80" />
        <el-table-column prop="download_count" label="下载数" width="80" />
        <el-table-column prop="favorite_count" label="收藏数" width="80" />
        <el-table-column label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_time) }}
          </template>
        </el-table-column>
        <el-table-column label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatDate(row.last_login_time) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editUser(row)">
              编辑
            </el-button>
            <el-button
              :type="row.status === 'banned' ? 'success' : 'warning'"
              size="small"
              @click="toggleUserStatus(row)"
            >
              {{ row.status === 'banned' ? '解封' : '封禁' }}
            </el-button>
            <el-button type="danger" size="small" @click="deleteUser(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="editForm.status" style="width: 100%">
            <el-option label="正常" value="active" />
            <el-option label="封禁" value="banned" />
            <el-option label="未激活" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item label="积分" prop="points">
          <el-input-number v-model="editForm.points" :min="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Refresh,
  Delete,
  Search,
  RefreshRight,
  UserFilled,
  Star,
  Lock
} from '@element-plus/icons-vue'
import {
  getUserList,
  getUserStats,
  updateUser,
  deleteUser as deleteUserApi,
  batchDeleteUsers,
  batchUpdateUserStatus,
  type User as UserType
} from '@/api/user'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const userList = ref<UserType[]>([])
const selectedUsers = ref<UserType[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 统计数据
const userStats = reactive({
  total: 0,
  active: 0,
  banned: 0
})

// 搜索表单
const searchForm = reactive({
  nickname: '',
  status: ''
})

// 编辑对话框
const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = reactive({
  _id: '',
  nickname: '',
  status: 'active',
  points: 0
})

// 表单验证规则
const editRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const query = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.nickname,
      status: searchForm.status
    }
    
    const result = await getUserList(query, pageSize.value, (currentPage.value - 1) * pageSize.value)
    
    if (result.success) {
      userList.value = result.data || []
      total.value = result.total || 0
    } else {
      ElMessage.error(result.message || '获取用户列表失败')
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchUserStats = async () => {
  try {
    const result = await getUserStats()
    if (result.success && result.data) {
      userStats.total = result.data.totalUsers || 0
      userStats.active = result.data.activeUsers || 0
      userStats.banned = result.data.bannedUsers || 0
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    nickname: '',
    status: ''
  })
  currentPage.value = 1
  fetchUserList()
}

// 刷新数据
const refreshData = () => {
  fetchUserList()
  fetchUserStats()
}

// 选择变化
const handleSelectionChange = (selection: UserType[]) => {
  selectedUsers.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchUserList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUserList()
}

// 编辑用户
const editUser = (user: UserType) => {
  Object.assign(editForm, {
    _id: user._id,
    nickname: user.nickname,
    status: user.status,
    points: user.points
  })
  editDialogVisible.value = true
}

// 保存用户
const saveUser = async () => {
  if (!editFormRef.value) return
  
  try {
    await editFormRef.value.validate()
    saving.value = true
    
    const updateData = {
      nickname: editForm.nickname,
      status: editForm.status,
      points: editForm.points
    }
    
    const result = await updateUser(editForm._id, updateData)
    
    if (result.success) {
      ElMessage.success('用户信息更新成功')
      editDialogVisible.value = false
      fetchUserList()
      fetchUserStats()
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('保存用户失败:', error)
  } finally {
    saving.value = false
  }
}

// 重置编辑表单
const resetEditForm = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 切换用户状态
const toggleUserStatus = async (user: UserType) => {
  const newStatus = user.status === 'banned' ? 'active' : 'banned'
  const action = newStatus === 'banned' ? '封禁' : '解封'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.nickname}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await updateUser(user._id!, { status: newStatus })
    
    if (result.success) {
      ElMessage.success(`${action}成功`)
      fetchUserList()
      fetchUserStats()
    } else {
      ElMessage.error(result.message || `${action}失败`)
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 删除用户
const deleteUser = async (user: UserType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.nickname}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await deleteUserApi(user._id!)
    
    if (result.success) {
      ElMessage.success('删除成功')
      fetchUserList()
      fetchUserStats()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const userIds = selectedUsers.value.map(user => user._id!)
    const result = await batchDeleteUsers(userIds)
    
    if (result.success) {
      ElMessage.success('批量删除成功')
      fetchUserList()
      fetchUserStats()
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 工具函数
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '正常',
    banned: '封禁',
    inactive: '未激活'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    banned: 'danger',
    inactive: 'info'
  }
  return typeMap[status] || ''
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化
onMounted(() => {
  fetchUserList()
  fetchUserStats()
})
</script>

<style scoped>
.user-manage {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48px;
  opacity: 0.1;
}

.stat-icon.total {
  color: #409eff;
}

.stat-icon.active {
  color: #67c23a;
}

.stat-icon.banned {
  color: #f56c6c;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.el-avatar {
  border: 2px solid #f0f0f0;
}
</style>