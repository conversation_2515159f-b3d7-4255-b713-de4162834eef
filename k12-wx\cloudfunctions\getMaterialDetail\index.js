// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { materialId, userId } = event
  
  try {
    // 获取资料详情
    const materialResult = await db.collection('files').doc(materialId).get()
    
    if (!materialResult.data) {
      return {
        success: false,
        message: '资料不存在'
      }
    }
    
    const material = materialResult.data
    
    // 增加浏览次数
    await db.collection('files').doc(materialId).update({
      data: {
        viewCount: db.command.inc(1)
      }
    })
    
    // 如果提供了用户ID，检查是否已收藏
    let isFavorited = false
    if (userId) {
      const favoriteResult = await db.collection('favorites').where({
        userId: userId,
        materialId: materialId
      }).get()
      
      isFavorited = favoriteResult.data.length > 0
    }
    
    // 获取相关资料推荐（同类别的其他资料）
    let relatedCondition = {
      _id: db.command.neq(materialId),
      status: '已上架'
    }
    
    // 如果是升学专区资料，按upgradeType推荐
    if (material.upgradeType) {
      relatedCondition.upgradeType = material.upgradeType
    } else {
      // 普通资料按学科推荐
      relatedCondition.subject = material.subject
    }
    
    const relatedResult = await db.collection('files')
      .where(relatedCondition)
      .limit(5)
      .orderBy('viewCount', 'desc')
      .get()
    
    return {
      success: true,
      data: {
        ...material,
        isFavorited: isFavorited,
        relatedMaterials: relatedResult.data
      }
    }
    
  } catch (error) {
    console.error('获取资料详情失败:', error)
    return {
      success: false,
      message: '获取资料详情失败，请重试',
      error: error.message
    }
  }
}