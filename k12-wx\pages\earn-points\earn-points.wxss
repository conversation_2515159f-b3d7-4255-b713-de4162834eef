/* pages/earn-points/earn-points.wxss */

page {
  background-color: #f8f9fa;
}

.container {
  padding: 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx 40rpx;
  text-align: center;
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.header-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 积分卡片 */
.points-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: -30rpx 30rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.points-info {
  flex: 1;
}

.points-label {
  display: block;
  color: #666;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.points-value {
  display: block;
  color: #333;
  font-size: 48rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.points-icon {
  font-size: 40rpx;
  margin-left: 20rpx;
}

/* 积分获取方式 */
.earn-methods {
  padding: 0 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  text-align: center;
}

.method-item {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.method-item:last-child {
  margin-bottom: 40rpx;
}

.method-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.method-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.method-content {
  flex: 1;
}

.method-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.method-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

.method-points {
  font-size: 24rpx;
  font-weight: bold;
  color: #ff6b35;
}

.method-status {
  font-size: 22rpx;
  padding: 10rpx 20rpx;
  border-radius: 50rpx;
  text-align: center;
  min-width: 100rpx;
  font-weight: 500;
}

/* 不同状态的样式 */
.status-available {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
}

.status-completed {
  background-color: #f0f9ff;
  color: #0ea5e9;
  border: 1rpx solid #bae6fd;
}

.status-unavailable {
  background-color: #f8f9fa;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}

/* 不同状态的方法项样式 */
.method-item.completed {
  opacity: 0.8;
  background-color: #fafafa;
}

.method-item.unavailable {
  opacity: 0.6;
  background-color: #fafafa;
}

.method-item.available {
  border-left: 4rpx solid #4285f4;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 200rpx 60rpx;
  color: #999;
}

.empty-state .icon {
  font-size: 80rpx;
  margin-bottom: 40rpx;
}

.empty-state .text {
  font-size: 28rpx;
}

/* 响应式优化 */
@media (max-width: 375px) {
  .header {
    padding: 50rpx 20rpx 30rpx;
  }
  
  .points-card {
    margin: -20rpx 20rpx 20rpx;
    padding: 30rpx;
  }
  
  .earn-methods {
    padding: 0 20rpx;
  }
  
  .method-item {
    padding: 28rpx 24rpx;
  }
}