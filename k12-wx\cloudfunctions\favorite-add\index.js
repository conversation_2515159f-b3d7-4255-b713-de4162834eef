// 云函数入口文件 - 添加收藏
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { materialId, userId } = event
  
  try {
    // 检查是否已经收藏
    const existQuery = await db.collection('favorites').where({
      userId: userId,
      materialId: materialId
    }).get()
    
    if (existQuery.data.length > 0) {
      return {
        success: false,
        message: '已经收藏过了'
      }
    }
    
    // 获取资料信息
    const materialResult = await db.collection('materials').doc(materialId).get()
    if (!materialResult.data) {
      return {
        success: false,
        message: '资料不存在'
      }
    }
    
    const material = materialResult.data
    
    // 添加收藏记录
    const favoriteResult = await db.collection('favorites').add({
      data: {
        userId: userId,
        materialId: materialId,
        materialTitle: material.title,
        materialGrade: material.grade,
        materialSubject: material.subject,
        materialType: material.type,
        materialThumbnail: material.thumbnailUrl,
        createTime: new Date()
      }
    })
    
    // 更新资料收藏数
    await db.collection('materials').doc(materialId).update({
      data: {
        favoriteCount: db.command.inc(1)
      }
    })
    
    // 更新用户收藏数
    await db.collection('users').doc(userId).update({
      data: {
        favoriteCount: db.command.inc(1)
      }
    })
    
    // 添加积分奖励（收藏奖励2积分）
    await db.collection('users').doc(userId).update({
      data: {
        points: db.command.inc(2)
      }
    })
    
    // 记录积分历史
    await db.collection('points_history').add({
      data: {
        userId: userId,
        type: 'favorite',
        points: 2,
        description: `收藏资料《${material.title}》`,
        targetId: materialId,
        createTime: new Date()
      }
    })
    
    return {
      success: true,
      data: {
        favoriteId: favoriteResult._id,
        pointsEarned: 2
      },
      message: '收藏成功，获得2积分'
    }
    
  } catch (error) {
    console.error('添加收藏失败:', error)
    return {
      success: false,
      message: '收藏失败，请重试',
      error: error.message
    }
  }
}