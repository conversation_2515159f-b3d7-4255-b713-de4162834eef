// pages/history/history.js
const app = getApp()

Page({
  data: {
    history: [],
    loading: false,
    isEmpty: false,
    sortType: 'time', // time: 按时间排序, name: 按名称排序
    filterGrade: '', // 年级筛选
    filterSubject: '', // 学科筛选
    grades: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三'],
    subjects: ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '科学', '道德与法治']
  },

  onLoad() {
    this.loadHistory()
  },

  onShow() {
    this.loadHistory()
  },

  // 加载浏览历史
  async loadHistory() {
    this.setData({ loading: true })
    
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，从云数据库获取浏览历史
        try {
          const params = {
            userId: userInfo.openid,
            page: 1,
            pageSize: 50,
            grade: this.data.filterGrade,
            subject: this.data.filterSubject
          }
          
          const result = await app.api.history.getHistoryList(params)
          
          if (result.success && result.data) {
            this.setData({
              history: result.data.list || [],
              isEmpty: (result.data.list || []).length === 0,
              loading: false
            })
          } else {
            // 云数据库获取失败，使用本地存储
            this.loadLocalHistory()
          }
        } catch (error) {
          console.error('从云数据库加载浏览历史失败:', error)
          // 降级到本地存储
          this.loadLocalHistory()
        }
      } else {
        // 未登录，使用本地存储
        this.loadLocalHistory()
      }
      
    } catch (error) {
      console.error('加载浏览历史失败:', error)
      this.setData({
        loading: false,
        isEmpty: true
      })
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 从本地存储加载浏览历史（降级方案）
  loadLocalHistory() {
    try {
      const history = wx.getStorageSync('viewHistory') || []
      
      // 按时间倒序排列
      history.sort((a, b) => new Date(b.viewTime) - new Date(a.viewTime))
      
      // 应用筛选条件
      let filteredHistory = history
      
      if (this.data.filterGrade) {
        filteredHistory = filteredHistory.filter(item => item.grade === this.data.filterGrade)
      }
      
      if (this.data.filterSubject) {
        filteredHistory = filteredHistory.filter(item => item.subject === this.data.filterSubject)
      }
      
      this.setData({
        history: filteredHistory,
        isEmpty: filteredHistory.length === 0,
        loading: false
      })
      
    } catch (error) {
      console.error('从本地存储加载浏览历史失败:', error)
      this.setData({
        loading: false,
        isEmpty: true
      })
    }
  },

  // 排序切换
  onSortChange(e) {
    const sortType = e.detail.value
    this.setData({ sortType })
    this.sortHistory(sortType)
  },

  // 排序历史记录
  sortHistory(sortType) {
    let history = [...this.data.history]
    
    if (sortType === 'time') {
      history.sort((a, b) => new Date(b.viewTime) - new Date(a.viewTime))
    } else if (sortType === 'name') {
      history.sort((a, b) => a.title.localeCompare(b.title))
    }
    
    this.setData({ history })
  },

  // 年级筛选
  onGradeFilter() {
    const grades = ['全部', ...this.data.grades]
    
    wx.showActionSheet({
      itemList: grades,
      success: (res) => {
        const selectedGrade = res.tapIndex === 0 ? '' : grades[res.tapIndex]
        this.setData({ filterGrade: selectedGrade })
        this.filterHistory()
      }
    })
  },

  // 学科筛选
  onSubjectFilter() {
    const subjects = ['全部', ...this.data.subjects]
    
    wx.showActionSheet({
      itemList: subjects,
      success: (res) => {
        const selectedSubject = res.tapIndex === 0 ? '' : subjects[res.tapIndex]
        this.setData({ filterSubject: selectedSubject })
        this.filterHistory()
      }
    })
  },

  // 筛选历史记录
  filterHistory() {
    try {
      let history = wx.getStorageSync('viewHistory') || []
      
      // 年级筛选
      if (this.data.filterGrade) {
        history = history.filter(item => item.grade === this.data.filterGrade)
      }
      
      // 学科筛选
      if (this.data.filterSubject) {
        history = history.filter(item => item.subject === this.data.filterSubject)
      }
      
      // 排序
      this.setData({ history })
      this.sortHistory(this.data.sortType)
      
    } catch (error) {
      console.error('筛选失败:', error)
    }
  },

  // 清空筛选
  clearFilter() {
    this.setData({
      filterGrade: '',
      filterSubject: ''
    })
    this.loadHistory()
  },

  // 查看资料详情
  viewMaterial(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 删除历史记录
  deleteHistory(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条浏览记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(id)
        }
      }
    })
  },

  // 执行删除
  async performDelete(id) {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，从云数据库删除
        try {
          const result = await app.api.history.removeHistory({
            userId: userInfo.openid,
            materialId: id
          })
          
          if (result.success) {
            // 云数据库删除成功，重新加载列表
            this.loadHistory()
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })
          } else {
            // 云数据库删除失败，降级到本地删除
            this.performLocalDelete(id)
          }
        } catch (error) {
          console.error('从云数据库删除浏览历史失败:', error)
          // 降级到本地删除
          this.performLocalDelete(id)
        }
      } else {
        // 未登录，删除本地存储
        this.performLocalDelete(id)
      }
      
    } catch (error) {
      console.error('删除失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 本地删除（降级方案）
  performLocalDelete(id) {
    try {
      let history = wx.getStorageSync('viewHistory') || []
      history = history.filter(item => item.id !== id)
      wx.setStorageSync('viewHistory', history)
      
      // 重新应用筛选
      this.loadLocalHistory()
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('本地删除失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 清空所有历史记录
  async clearAll() {
    if (this.data.history.length === 0) return
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有浏览记录吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const userInfo = wx.getStorageSync('userInfo') || {}
            
            if (userInfo.openid) {
              // 如果已登录，从云数据库清空
              try {
                const result = await app.api.history.clearAllHistory({
                  userId: userInfo.openid
                })
                
                if (result.success) {
                  console.log('云数据库清空成功')
                } else {
                  console.warn('云数据库清空失败，继续清空本地存储')
                }
              } catch (error) {
                console.error('云数据库清空失败:', error)
              }
            }
            
            // 无论云数据库是否成功，都清空本地存储
            wx.removeStorageSync('viewHistory')
            this.setData({
              history: [],
              isEmpty: true
            })
            
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空失败:', error)
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadHistory()
    wx.stopPullDownRefresh()
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '浏览历史 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})