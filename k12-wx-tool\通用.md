cmd.md是一个小程序的基础开发需求。让我们一些完善一个PRD文档。文档存放在RPD文件夹下。

第一部分内容是，先总结一下小程序是干什么的，有哪些用途


根据01-产品概述.md,继续创建第二部分 02-用户端界面设计.md、


接下来根据 01 02 两个文件 ，完成03-用户功能需求详述.md



根据01-03三个文件。现在开始设计04-后台管理系统概述.md

后台管理系统在本地运行，调用小程序开发云环境的云函数与云数据，实现管理功能。




D:\code\k12-wx-tool\PRD 阅读下面的5个文件，开始进行小程序的数据库设计部分，要求小程序用户端与后台管理系统都适配。

写入文档06-前后端通用数据库.md，要求： 1 完成小程序与后台管理系统的功能即可，不需要太多衍生功能   2 给出每个字段的详细说明及功能说明   3 不用写太多额外的内容，聚焦于数据库的设计即可


# 数据库设计 -自动化

阅读D:\code\k12-wx-tool\PRD下的内容，尤其是编号为06的文件，写一个文件如何在小程序云开发环境，ID：cloud1-8gm001v7fd56ff43 中创建06中所述的数据库。

文件存在data-tool下，名称为创建数据库说明.md
要求：1 选择合适的工具，比如codebuddy IDE 国际版中链接云开发环境  2 批量创建为主

功能需求详述
数据库设计
技术实现方案
运营策略
项目开发计划等


数据库配置完成，现在我们先开发后台管理系统，请阅读D:\code\wx\k12-wx-tool\PRD 中关于后台管理系统的实现方式，在 D:\code\wx\k12-admin中构建项目


k12-wx-tool\PRD\04-后台管理系统.md
k12-wx-tool\PRD\05-后台管理系统界面设计.md
k12-wx-tool\PRD\06-前后端通用数据库.md

根据以上文件，目前完成的功能只有文件单个删除功能，请继续完成后台管理系统开发。
小程序对应的云数据库都已经搭建好，无需再动。


根据以下文件
k12-wx-tool\PRD\04-后台管理系统.md
k12-wx-tool\PRD\05-后台管理系统界面设计.md
k12-wx-tool\PRD\06-前后端通用数据库.md

校验k12-admin还有那些功能没实现。


阅读 k12-wx-tool\PRD\04-后台管理系统.md
k12-wx-tool\PRD\05-后台管理系统界面设计.md
k12-wx-tool\PRD\06-前后端通用数据库.md
