<template>
  <div class="dashboard">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <h2>数据概览</h2>
      <p class="page-description">K12教辅资料管理系统 - 实时数据统计与分析面板</p>
    </div>

    <!-- 核心统计指标卡片区域 - 展示系统关键运营数据 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <!-- 文件总数统计卡片 - 显示系统中所有教辅资料文件的总数量 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon files">
                <el-icon size="28"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.fileCount }}</div>
                <div class="stats-label">文件总数</div>
                <div class="stats-description">系统中所有教辅资料文件数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 用户总数统计卡片 - 显示注册用户总数 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon users">
                <el-icon size="28"><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.userCount }}</div>
                <div class="stats-label">用户总数</div>
                <div class="stats-description">累计注册用户数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 总下载量统计卡片 - 显示所有文件的累计下载次数 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon downloads">
                <el-icon size="28"><Download /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.downloadCount }}</div>
                <div class="stats-label">总下载量</div>
                <div class="stats-description">所有文件累计下载次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 今日上传统计卡片 - 显示当天新增的文件数量 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon uploads">
                <el-icon size="28"><Upload /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.todayUploadCount }}</div>
                <div class="stats-label">今日上传</div>
                <div class="stats-description">今天新增的文件数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 活跃用户统计卡片 - 显示最近30天内有活动的用户数 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active-users">
                <el-icon size="28"><UserFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.activeUserCount }}</div>
                <div class="stats-label">活跃用户</div>
                <div class="stats-description">最近30天内活跃用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 存储使用量统计卡片 - 显示云存储空间使用情况 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon storage">
                <el-icon size="28"><DataBoard /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardStats.storageUsed }}</div>
                <div class="stats-label">存储使用</div>
                <div class="stats-description">云存储空间使用量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据可视化图表区域 - 提供多维度数据分析视图 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 文件上传趋势图 - 显示最近30天的文件上传数量变化趋势 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>文件上传趋势</span>
                <small class="chart-subtitle">最近30天教辅资料上传数量变化</small>
                <el-button type="text" @click="refreshUploadTrend">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart 
                class="chart" 
                :option="uploadTrendOption" 
                :loading="uploadTrendLoading"
              />
            </div>
          </el-card>
        </el-col>
        
        <!-- 下载量趋势图 - 显示最近30天的文件下载数量变化趋势 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>下载量趋势</span>
                <small class="chart-subtitle">最近30天用户下载行为统计</small>
                <el-button type="text" @click="refreshDownloadTrend">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart 
                class="chart" 
                :option="downloadTrendOption"
                :loading="downloadTrendLoading"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 文件分类分布图 - 饼图显示各学科教辅资料的数量分布 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>文件分类分布</span>
                <small class="chart-subtitle">各学科教辅资料数量占比</small>
                <el-button type="text" @click="refreshCategoryDistribution">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart 
                class="chart" 
                :option="categoryDistributionOption"
                :loading="categoryDistributionLoading"
              />
            </div>
          </el-card>
        </el-col>
        
        <!-- 热门文件下载排行榜 - 显示下载量最高的教辅资料TOP10 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>热门文件下载排行</span>
                <small class="chart-subtitle">下载量最高的教辅资料TOP10</small>
                <el-button type="text" @click="refreshPopularFiles">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="popular-files">
              <!-- 文件排行项 - 显示排名、文件名、分类和下载次数 -->
              <div class="file-rank-item" v-for="(file, index) in popularFiles" :key="file.id">
                <div class="rank-number" :class="getRankClass(index)">{{ index + 1 }}</div>
                <div class="file-info">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">
                    <span class="file-category">{{ file.category }}</span>
                  </div>
                </div>
                <div class="download-count">
                  <el-icon><Download /></el-icon>
                  {{ file.downloadCount }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 用户活跃度统计图 - 柱状图显示最近30天的用户活跃情况 -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>用户活跃度统计</span>
                <small class="chart-subtitle">最近30天每日活跃用户数量</small>
                <el-button type="text" @click="refreshUserActivity">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="chart-container">
              <v-chart 
                class="chart" 
                :option="userActivityOption"
                :loading="userActivityLoading"
              />
            </div>
          </el-card>
        </el-col>
        
        <!-- 最近活动时间轴 - 显示用户最新的操作记录（上传、下载、分享） -->
        <el-col :xs="24" :sm="12" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>最近活动</span>
                <small class="chart-subtitle">用户最新操作记录（上传/下载/分享）</small>
                <el-button type="text" @click="refreshRecentActivity">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="recent-activity">
              <!-- 活动时间轴 - 按时间倒序显示用户操作记录 -->
              <el-timeline>
                <el-timeline-item
                  v-for="activity in recentActivities"
                  :key="activity.id"
                  :timestamp="formatTime(activity.time)"
                  :type="getActivityType(activity.type)"
                >
                  <div class="activity-content">
                    <div class="activity-description">{{ getActivityDescription(activity) }}</div>
                    <div class="activity-user">{{ activity.userName }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  Document,
  User,
  UserFilled,
  Download,
  Upload,
  DataBoard,
  Refresh
} from '@element-plus/icons-vue'
import {
  getDashboardStats,
  getUploadTrendData,
  getDownloadTrendData,
  getCategoryDistribution,
  getUserActivityData,
  getHotFiles,
  getRecentActivities,
  type DashboardStats,
  type CategoryDistribution,
  type HotFile,
  type RecentActivity
} from '../api/statistics'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 统计数据
const dashboardStats = ref<DashboardStats>({
  fileCount: 0,
  userCount: 0,
  activeUserCount: 0,
  downloadCount: 0,
  todayUploadCount: 0,
  storageUsed: '0 B'
})

// 图表加载状态
const uploadTrendLoading = ref(false)
const downloadTrendLoading = ref(false)
const categoryDistributionLoading = ref(false)
const userActivityLoading = ref(false)

// 热门文件数据
const popularFiles = ref<HotFile[]>([])

// 最近活动数据
const recentActivities = ref<RecentActivity[]>([])

// 文件上传趋势图配置
const uploadTrendOption = ref({
  title: {
    text: '最近30天上传趋势',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [] as number[],
    type: 'line',
    smooth: true,
    itemStyle: {
      color: '#409EFF'
    }
  }]
})

// 下载量趋势图配置
const downloadTrendOption = ref({
  title: {
    text: '最近30天下载趋势',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [] as number[],
    type: 'line',
    smooth: true,
    itemStyle: {
      color: '#67C23A'
    }
  }]
})

// 文件分类分布图配置
const categoryDistributionOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: '5%',
    top: 'center',
    textStyle: {
      fontSize: 12
    },
    itemWidth: 14,
    itemHeight: 14,
    itemGap: 10,
    width: '25%'
  },
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    containLabel: true
  },
  series: [{
    name: '文件分类',
    type: 'pie',
    radius: ['35%', '65%'],
    center: ['65%', '50%'],
    data: [] as CategoryDistribution[],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    label: {
      show: true,
      formatter: '{d}%',
      fontSize: 10,
      fontWeight: 'normal',
      position: 'inside',
      color: '#fff'
    },
    labelLine: {
      show: false
    }
  }]
})

// 用户活跃度统计图配置
const userActivityOption = ref({
  title: {
    text: '最近30天用户活跃度',
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '活跃用户数',
    data: [] as number[],
    type: 'bar',
    itemStyle: {
      color: '#E6A23C'
    }
  }]
})

// 工具函数
const getRankClass = (index: number): string => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

const formatTime = (timeString: string): string => {
  try {
    const date = new Date(timeString)
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timeString
  }
}

const getActivityType = (type: string): string => {
  const typeMap: Record<string, string> = {
    upload: 'success',
    download: 'primary',
    share: 'warning'
  }
  return typeMap[type] || 'info'
}

const getActivityDescription = (activity: RecentActivity): string => {
  const actionMap: Record<string, string> = {
    upload: '上传了文件',
    download: '下载了文件',
    share: '分享了文件'
  }
  return `${actionMap[activity.type] || '操作了文件'}：${activity.fileName}`
}

// 加载统计数据
const loadDashboardStats = async () => {
  try {
    const stats = await getDashboardStats()
    dashboardStats.value = stats
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 加载上传趋势数据
const loadUploadTrendData = async () => {
  uploadTrendLoading.value = true
  try {
    const data = await getUploadTrendData()
    uploadTrendOption.value.xAxis.data = data.map(item => item.date.substring(5))
    uploadTrendOption.value.series[0].data = data.map(item => item.count)
  } catch (error) {
    console.error('加载上传趋势数据失败:', error)
    ElMessage.error('加载上传趋势数据失败')
  } finally {
    uploadTrendLoading.value = false
  }
}

// 加载下载趋势数据
const loadDownloadTrendData = async () => {
  downloadTrendLoading.value = true
  try {
    const data = await getDownloadTrendData()
    downloadTrendOption.value.xAxis.data = data.map(item => item.date.substring(5))
    downloadTrendOption.value.series[0].data = data.map(item => item.count)
  } catch (error) {
    console.error('加载下载趋势数据失败:', error)
    ElMessage.error('加载下载趋势数据失败')
  } finally {
    downloadTrendLoading.value = false
  }
}

// 加载分类分布数据
const loadCategoryDistribution = async () => {
  categoryDistributionLoading.value = true
  try {
    const data = await getCategoryDistribution()
    categoryDistributionOption.value.series[0].data = data
  } catch (error) {
    console.error('加载分类分布数据失败:', error)
    ElMessage.error('加载分类分布数据失败')
  } finally {
    categoryDistributionLoading.value = false
  }
}

// 加载用户活跃度数据
const loadUserActivityData = async () => {
  userActivityLoading.value = true
  try {
    const data = await getUserActivityData()
    userActivityOption.value.xAxis.data = data.map(item => item.date.substring(5))
    userActivityOption.value.series[0].data = data.map(item => item.activeUsers)
  } catch (error) {
    console.error('加载用户活跃度数据失败:', error)
    ElMessage.error('加载用户活跃度数据失败')
  } finally {
    userActivityLoading.value = false
  }
}

// 加载热门文件数据
const loadHotFiles = async () => {
  try {
    const data = await getHotFiles(10)
    popularFiles.value = data
  } catch (error) {
    console.error('加载热门文件数据失败:', error)
    ElMessage.error('加载热门文件数据失败')
  }
}

// 加载最近活动数据
const loadRecentActivities = async () => {
  try {
    const data = await getRecentActivities(20)
    recentActivities.value = data
  } catch (error) {
    console.error('加载最近活动数据失败:', error)
    ElMessage.error('加载最近活动数据失败')
  }
}

// 刷新函数
const refreshUploadTrend = () => {
  loadUploadTrendData()
}

const refreshDownloadTrend = () => {
  loadDownloadTrendData()
}

const refreshCategoryDistribution = () => {
  loadCategoryDistribution()
}

const refreshUserActivity = () => {
  loadUserActivityData()
}

const refreshPopularFiles = () => {
  loadHotFiles()
}

const refreshRecentActivity = () => {
  loadRecentActivities()
}

// 加载所有数据
const loadDashboardData = async () => {
  await Promise.all([
    loadDashboardStats(),
    loadUploadTrendData(),
    loadDownloadTrendData(),
    loadCategoryDistribution(),
    loadUserActivityData(),
    loadHotFiles(),
    loadRecentActivities()
  ])
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.stats-icon.files {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-icon.users {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stats-icon.downloads {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stats-icon.uploads {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stats-icon.active-users {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stats-icon.storage {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.stats-description {
  font-size: 10px;
  color: #C0C4CC;
  margin-top: 2px;
  line-height: 1.2;
  display: block;
}

.chart-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  font-weight: normal;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

.card-header small {
  flex-basis: 100%;
  margin-top: 4px;
  order: 2;
}

.card-header .el-button {
  order: 3;
  margin-left: auto;
}

.charts-section {
  margin-top: 24px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.chart-container .chart {
  margin: 0 !important;
  padding: 0 !important;
}

.chart {
  height: 100%;
  width: 100%;
}

.popular-files {
  height: 320px;
  overflow-y: auto;
  padding: 0 16px;
}

.file-rank-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-rank-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.rank-first {
  background: #FFD700;
  color: white;
}

.rank-second {
  background: #C0C0C0;
  color: white;
}

.rank-third {
  background: #CD7F32;
  color: white;
}

.rank-normal {
  background: #f5f5f5;
  color: #666;
}

.file-info {
  flex: 1;
  margin-right: 12px;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.download-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #67C23A;
  font-weight: 600;
}

.recent-activity {
  height: 320px;
  overflow-y: auto;
  padding: 0 16px;
}

.activity-content {
  margin-bottom: 4px;
}

.activity-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.activity-user {
  font-size: 12px;
  color: #909399;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .stats-card {
    height: 80px;
  }
  
  .stats-icon {
    width: 40px;
    height: 40px;
  }
  
  .stats-number {
    font-size: 20px;
  }
  
  .chart-card {
    height: 300px;
  }
  
  .chart-container {
    height: 220px;
  }
  
  .popular-files,
  .recent-activity {
    height: 220px;
  }
}
</style>