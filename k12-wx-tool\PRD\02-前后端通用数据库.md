# K12教育资料小程序数据库设计文档

## 1. 数据库概述

### 1.1 数据库类型
- **前端（小程序）**: 微信云数据库（基于MongoDB的NoSQL数据库）
- **后端（管理系统）**: 本地数据库（可使用SQLite或MySQL）

### 1.2 设计原则
- 数据结构统一，支持前后端数据同步
- 支持4维分类体系（年级、学科、学期、类别）
- 积分系统完整记录
- 用户行为数据完整追踪
- 系统配置灵活可调
- 分类筛选基于files集合动态统计，确保数据实时性

### 1.3 数据表总览
本数据库设计包含10个核心数据表：
1. 用户表 (users)
2. 文件表 (files) 
3. 积分记录表 (point_records)
4. 下载记录表 (downloads)
5. 收藏表 (favorites)
6. 分享记录表 (shares)
7. 用户反馈表 (feedbacks)
8. 用户邀请表 (invitations)
9. 用户每日积分获取记录表 (daily_point_limits)
10. 系统配置表 (system_configs)

## 2. 数据表详细设计

### 2.1 用户表 (users)

**表说明**: 存储小程序用户的基本信息和积分状态

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 用户唯一标识 |
| openid | String | 是 | - | 微信用户唯一标识 |
| nickname | String | 否 | "用户" | 用户昵称 |
| avatar_url | String | 否 | "" | 用户头像URL |
| points | Number | 是 | 200 | 当前积分余额 |
| total_points_earned | Number | 是 | 200 | 累计获得积分 |
| total_points_spent | Number | 是 | 0 | 累计消费积分 |
| download_count | Number | 是 | 0 | 下载次数统计 |
| favorite_count | Number | 是 | 0 | 收藏次数统计 |
| share_count | Number | 是 | 0 | 分享次数统计 |
| last_login_time | Date | 否 | - | 最后登录时间 |
| created_time | Date | 是 | 当前时间 | 注册时间 |
| updated_time | Date | 是 | 当前时间 | 更新时间 |
| status | String | 是 | "active" | 用户状态：active/inactive/banned |

**索引设计**:
- 主键索引：_id
- 唯一索引：openid
- 普通索引：status, created_time

### 2.2 文件表 (files)

**表说明**: 存储教育资料文件的详细信息，同时作为分类筛选的数据源

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 文件唯一标识 |
| title | String | 是 | - | 文件标题 |
| description | String | 否 | "" | 文件描述 |
| file_url | String | 是 | - | 文件存储URL |
| file_type | String | 是 | - | 文件类型：pdf/doc/ppt/xls/zip等 |
| file_size | Number | 是 | - | 文件大小（字节） |
| thumbnail_url | String | 否 | "" | 缩略图URL |
| preview_images | Array | 否 | [] | 资料预览图数组，最多3张，格式：[{url: "图片URL", order: 1}] |
| grade | String | 是 | - | 年级：一年级/二年级/三年级/四年级/五年级/六年级/升学专区 |
| subject | String | 是 | - | 学科：语文/数学/英语/科学/音乐/美术/体育 |
| volume | String | 是 | - | 册别：上册/下册/全册/专区 |
| section | String | 是 | - | 板块：素材资源/专项练习/单元同步/教案学案/试卷/课件/专项资料 |
| upgradeType | String | 否 | null | 升学专区类型：幼升小/小升初 |
| upgradeCategory | String | 否 | null | 升学专区分类：拼音启蒙/认识数字/习惯养成/学科启蒙/知识科普/语文冲刺/数学冲刺/英语强化/真题模拟/面试准备 |
| tags | Array | 否 | [] | 标签数组 |
| points | Number | 是 | 10 | 下载所需积分 |
| download_count | Number | 是 | 0 | 下载次数 |
| favorite_count | Number | 是 | 0 | 收藏次数 |
| share_count | Number | 是 | 0 | 分享次数 |
| view_count | Number | 是 | 0 | 浏览次数 |
| uploader_id | String | 否 | "system" | 上传者ID |
| upload_time | Date | 是 | 当前时间 | 上传时间 |
| updated_time | Date | 是 | 当前时间 | 更新时间 |
| status | String | 是 | "active" | 文件状态：active/inactive/deleted |
| is_featured | Boolean | 是 | false | 是否推荐 |
| sort_order | Number | 是 | 0 | 排序权重 |

**索引设计**:
- 主键索引：_id
- 复合索引：grade + subject + volume + section
- 普通索引：status, is_featured, upload_time, download_count

**分类筛选说明**:
- 年级筛选：基于grade字段动态统计，自动过滤升学专区（幼升小、小升初）
- 学科筛选：基于subject字段动态统计，按"语文、数学、英语"优先排序
- 册别筛选：基于volume字段动态统计
- 板块筛选：基于section字段动态统计

**板块分类标准值**:
- 单元同步
- 单元知识点
- 核心知识点
- 期中期末试卷
- 专项练习
- 教案学案
- 课件
- 素材资源

> 注：板块分类可在后台上传文件时自定义，上述为标准推荐值

### 2.3 积分记录表 (point_records)

**表说明**: 记录用户积分的获得和消费明细

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 记录唯一标识 |
| user_id | String | 是 | - | 用户ID |
| type | String | 是 | - | 积分类型：earn/spend |
| points | Number | 是 | - | 积分数量（正数为获得，负数为消费） |
| action | String | 是 | - | 触发动作：register/daily_login/download/share/ad_watch/invite/admin_adjust |
| related_id | String | 否 | "" | 关联对象ID（如文件ID） |
| description | String | 是 | - | 积分变动描述 |
| balance_before | Number | 是 | - | 变动前余额 |
| balance_after | Number | 是 | - | 变动后余额 |
| created_time | Date | 是 | 当前时间 | 创建时间 |
| operator_id | String | 否 | "system" | 操作者ID |

**索引设计**:
- 主键索引：_id
- 复合索引：user_id + created_time
- 普通索引：type, action, created_time

### 2.4 下载记录表 (downloads)

**表说明**: 记录用户的文件下载历史

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 下载记录唯一标识 |
| user_id | String | 是 | - | 用户ID |
| file_id | String | 是 | - | 文件ID |
| points_spent | Number | 是 | - | 消费积分数 |
| download_time | Date | 是 | 当前时间 | 下载时间 |
| ip_address | String | 否 | "" | 下载IP地址 |
| user_agent | String | 否 | "" | 用户代理信息 |

**索引设计**:
- 主键索引：_id
- 复合索引：user_id + download_time
- 复合索引：file_id + download_time
- 唯一索引：user_id + file_id（防止重复下载）

### 2.5 收藏表 (favorites)

**表说明**: 记录用户的文件收藏

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 收藏记录唯一标识 |
| user_id | String | 是 | - | 用户ID |
| file_id | String | 是 | - | 文件ID |
| created_time | Date | 是 | 当前时间 | 收藏时间 |

**索引设计**:
- 主键索引：_id
- 复合索引：user_id + created_time
- 复合索引：file_id + created_time
- 唯一索引：user_id + file_id（防止重复收藏）

### 2.6 分享记录表 (shares)

**表说明**: 记录用户的文件分享行为

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 分享记录唯一标识 |
| user_id | String | 是 | - | 分享用户ID |
| file_id | String | 是 | - | 分享文件ID |
| share_type | String | 是 | - | 分享类型：wechat/moments/copy_link |
| points_earned | Number | 是 | 0 | 获得积分数 |
| created_time | Date | 是 | 当前时间 | 分享时间 |

**索引设计**:
- 主键索引：_id
- 复合索引：user_id + created_time
- 复合索引：file_id + created_time
- 普通索引：share_type

### 2.7 用户反馈表 (feedbacks)

**表说明**: 记录用户的意见反馈和问题建议

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 反馈唯一标识 |
| user_id | String | 是 | - | 用户ID |
| content | String | 是 | - | 反馈内容 |
| contact | String | 否 | "" | 联系方式 |
| status | String | 是 | "pending" | 状态：pending/processing/resolved/rejected |
| admin_reply | String | 否 | "" | 管理员回复 |
| created_time | Date | 是 | 当前时间 | 创建时间 |
| updated_time | Date | 是 | 当前时间 | 更新时间 |

**索引设计**:
- 主键索引：_id
- 复合索引：user_id + created_time
- 普通索引：status, created_time

### 2.8 用户邀请表 (invitations)

**表说明**: 记录用户邀请关系

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 邀请记录唯一标识 |
| inviter_id | String | 是 | - | 邀请人ID |
| invitee_id | String | 是 | - | 被邀请人ID |
| points_earned | Number | 是 | 0 | 获得积分数 |
| created_time | Date | 是 | 当前时间 | 邀请时间 |

**索引设计**:
- 主键索引：_id
- 复合索引：inviter_id + created_time
- 唯一索引：invitee_id（确保每个用户只被邀请一次）

### 2.9 用户每日积分获取记录表 (daily_point_limits)

**表说明**: 记录用户每日积分获取情况，用于控制每日积分获取次数限制

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 记录唯一标识 |
| user_id | String | 是 | - | 用户ID |
| date | Date | 是 | 当前日期 | 日期（不含时间） |
| share_count | Number | 是 | 0 | 当日分享次数 |
| ad_watch_count | Number | 是 | 0 | 当日观看广告次数 |
| invite_count | Number | 是 | 0 | 当日邀请次数 |
| has_signed | Boolean | 是 | false | 当日是否已签到 |

**索引设计**:
- 主键索引：_id
- 唯一索引：user_id + date（确保每个用户每天只有一条记录）

### 2.10 系统配置表 (system_configs)

**表说明**: 存储系统的各项配置参数，包括积分获取方式的开关和数量设置

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 |
|--------|----------|----------|--------|------|
| _id | ObjectId | 是 | 自动生成 | 配置唯一标识 |
| key | String | 是 | - | 配置键名 |
| value | String | 是 | - | 配置值 |
| type | String | 是 | "string" | 数据类型：string/number/boolean/json |
| category | String | 是 | "general" | 配置分类：general/points/files/ui |
| description | String | 否 | "" | 配置描述 |
| is_editable | Boolean | 是 | true | 是否可编辑 |
| created_time | Date | 是 | 当前时间 | 创建时间 |
| updated_time | Date | 是 | 当前时间 | 更新时间 |

**系统配置示例**:

**积分系统配置**:
| key | value | type | category | description |
|-----|-------|------|----------|-------------|
| share_points | "50" | number | points | 分享获取积分数量 |
| share_enabled | "true" | boolean | points | 是否启用分享获取积分 |
| share_daily_limit | "3" | number | points | 每日分享获取积分次数限制 |
| ad_watch_points | "100" | number | points | 观看广告获取积分数量 |
| ad_watch_enabled | "true" | boolean | points | 是否启用观看广告获取积分 |
| ad_watch_daily_limit | "5" | number | points | 每日观看广告获取积分次数限制 |
| invite_points | "100" | number | points | 邀请获取积分数量 |
| invite_enabled | "true" | boolean | points | 是否启用邀请获取积分 |
| invite_daily_limit | "3" | number | points | 每日邀请获取积分次数限制 |
| daily_login_points | "50" | number | points | 每日签到获取积分数量 |
| daily_login_enabled | "true" | boolean | points | 是否启用每日签到获取积分 |
| new_user_points | "200" | number | points | 新用户默认获取积分数量 |

**搜索功能配置**:
| key | value | type | category | description |
|-----|-------|------|----------|-------------|
| hot_keywords | "单元,知识点,练习,试卷" | string | search | 热搜关键词（逗号分隔） |
| hot_keywords_enabled | "true" | boolean | search | 是否启用热搜关键词功能 |
| hot_keywords_max_count | "10" | number | search | 热搜关键词最大显示数量 |

**热搜词配置JSON格式示例**:
```json
{
  "_id": "2ccbb815689730130135ff99776103f1",
  "category": "search",
  "created_time": "Sat Aug 09 2025 19:25:06 GMT+0800 (中国标准时间)",
  "description": "热门搜索关键词",
  "is_editable": true,
  "key": "hot_keywords",
  "type": "string",
  "updated_time": "Sat Aug 09 2025 19:25:06 GMT+0800 (中国标准时间)",
  "value": "单元,知识点,练习,试卷"
}
```
