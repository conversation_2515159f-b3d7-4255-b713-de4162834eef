<view class="container">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-box">
      <view class="search-icon">
        <image src="/images/search-icon.png" class="search-icon-img" mode="aspectFit" />
      </view>
      <input 
        class="search-input" 
        placeholder="搜索试卷、练习册、教案..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        focus="{{searchFocus}}"
        confirm-type="search"
        placeholder-class="search-placeholder"
      />
      <view class="clear-btn" wx:if="{{searchKeyword}}" bindtap="clearSearch">
        <view class="clear-icon">×</view>
      </view>
    </view>
  </view>

  <!-- 搜索建议 -->
  <view class="search-suggestions" wx:if="{{showSuggestions && searchSuggestions.length > 0}}">
    <view class="suggestion-list">
      <view 
        class="suggestion-item" 
        wx:for="{{searchSuggestions}}" 
        wx:key="*this"
        bindtap="selectSuggestion"
        data-keyword="{{item}}"
      >
        <view class="suggestion-icon">🔍</view>
        <text class="suggestion-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!searchKeyword && !showSuggestions && searchHistory.length > 0}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <text class="clear-history" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-tags">
      <text 
        class="history-tag" 
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="selectHistory"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>

  <!-- 热门搜索 -->
  <view class="hot-search" wx:if="{{!searchKeyword && !showSuggestions && hotKeywords.length > 0}}">
    <view class="hot-header">
      <text class="hot-title">热门搜索</text>
    </view>
    <view class="hot-tags">
      <text 
        class="hot-tag" 
        wx:for="{{hotKeywords}}" 
        wx:key="*this"
        bindtap="selectHotKeyword"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>


  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="result-header">
      <text class="result-count">找到 {{totalCount}} 个结果</text>
    </view>
    <view class="result-list">
      <view 
        class="result-item" 
        wx:for="{{searchResults}}" 
        wx:key="_id"
        bindtap="goToDetail"
        data-id="{{item._id}}"
      >
        <image class="result-cover" src="{{item.preview_images[0] || '/images/default-cover.png'}}" mode="aspectFill" />
        <view class="result-info">
          <view class="result-title ellipsis-2">{{item.title}}</view>
          <view class="result-tags">
            <text class="tag">{{item.grade}}</text>
            <text class="tag">{{item.subject}}</text>
            <text class="tag">{{item.volume}}</text>
            <text class="tag">{{item.section}}</text>
          </view>
          <view class="result-stats">
            <text class="stat-item">⬇ {{item.download_count || item.downloads || 0}}</text>
            <text class="stat-item">👁 {{item.view_count || 0}}</text>
          </view>
          <view class="result-price text-function">{{item.points}}积分</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>搜索中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{searchResults.length > 0 && !loading}}">
    <view class="load-more-content" wx:if="{{loadingMore}}">
      <text>加载中...</text>
    </view>
    <view class="load-more-content" wx:elif="{{!hasMore}}">
      <text>没有更多了</text>
    </view>
    <view class="load-more-content" wx:else>
      <text>上拉加载更多</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && searchKeyword && searchResults.length === 0}}">
    <view class="empty-icon">🔍</view>
    <view class="empty-text">未找到相关资料</view>
    <view class="empty-desc">试试其他关键词</view>
  </view>

</view>