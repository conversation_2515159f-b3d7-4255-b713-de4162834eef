import { cloudbaseRequest } from './cloudbase'

// 用户接口类型定义（基于微信登录）
export interface User {
  _id: string
  openid: string
  nickname: string
  avatar_url: string
  points: number
  total_points_earned: number
  total_points_spent: number
  download_count: number
  favorite_count: number
  share_count: number
  status: 'active' | 'inactive' | 'banned'
  created_time: string
  last_login_time: string
  updated_time: string
}

export interface UserQuery {
  page: number
  pageSize: number
  keyword?: string
  status?: string
  level?: number
  startDate?: string
  endDate?: string
}

export interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  bannedUsers: number
  levelDistribution: { level: number; count: number }[]
  registrationTrend: { date: string; count: number }[]
}

// 获取用户列表
export const getUserList = async (query: UserQuery, limit: number = 20, offset: number = 0) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'list',
    data: {
      ...query,
      pageSize: limit,
      page: Math.floor(offset / limit) + 1
    }
  })
}

// 获取用户详情
export const getUserDetail = async (userId: string) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'get',
    data: { _id: userId }
  })
}

// 更新用户信息
export const updateUser = async (userId: string, userData: Partial<User>) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'update',
    data: {
      _id: userId,
      ...userData
    }
  })
}

// 删除用户
export const deleteUser = async (userId: string) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'delete',
    data: { _id: userId }
  })
}

// 批量删除用户
export const batchDeleteUsers = async (userIds: string[]) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'batchDelete',
    data: { ids: userIds }
  })
}

// 批量更新用户状态
export const batchUpdateUserStatus = async (userIds: string[], status: string) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'batchUpdate',
    data: {
      ids: userIds,
      status
    }
  })
}

// 重置用户密码
export const resetUserPassword = async (userId: string) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'resetPassword',
    data: { _id: userId }
  })
}

// 获取用户统计数据
export const getUserStats = async () => {
  const result = await cloudbaseRequest({
    collection: 'users',
    action: 'stats'
  })
  
  if (result.success) {
    return {
      success: true,
      data: {
        totalUsers: result.data?.totalUsers || 0,
        activeUsers: result.data?.activeUsers || 0,
        newUsersToday: result.data?.newUsersToday || 0,
        bannedUsers: result.data?.bannedUsers || 0,
        levelDistribution: result.data?.levelDistribution || [],
        registrationTrend: result.data?.registrationTrend || []
      }
    }
  }
  
  return result
}

// 导出用户数据
export const exportUsers = async (query: UserQuery) => {
  return await cloudbaseRequest({
    collection: 'users',
    action: 'export',
    data: query
  })
}

// 发送系统消息给用户
export const sendMessageToUser = async (userId: string, message: string) => {
  return await cloudbaseRequest({
    collection: 'messages',
    action: 'create',
    data: {
      userId,
      message,
      type: 'system',
      createTime: new Date().toISOString()
    }
  })
}