/* pages/my-downloads/my-downloads.wxss */
.container {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 32rpx 20rpx;
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  color: white;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-bottom: 1rpx solid #E6E6E6;
  margin-bottom: 16rpx;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  font-size: 28rpx;
}

.filter-label {
  color: #666666;
  margin-right: 8rpx;
}

.filter-value {
  color: #333333;
  margin-right: 8rpx;
}

.filter-arrow {
  color: #999999;
  font-size: 20rpx;
}

.clear-filter {
  color: #1677FF;
  font-size: 28rpx;
}

.sort-section {
  flex-shrink: 0;
}

.sort-picker {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  font-size: 28rpx;
}

.sort-label {
  color: #333333;
  margin-right: 8rpx;
}

.sort-arrow {
  color: #999999;
  font-size: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E6E6E6;
  border-top: 4rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666666;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.empty-button {
  padding: 24rpx 48rpx;
  background-color: #1677FF;
  color: white;
  border-radius: 48rpx;
  font-size: 28rpx;
  text-decoration: none;
}

/* 下载列表 */
.download-list {
  padding: 0 32rpx;
}

.download-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-content {
  display: flex;
  padding: 24rpx;
}

.item-cover {
  position: relative;
  width: 120rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-cover image {
  width: 100%;
  height: 100%;
}

.item-type {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 4rpx 8rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  border-radius: 4rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-meta {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #666666;
}

.meta-separator {
  margin: 0 8rpx;
  color: #999999;
}

.item-time {
  font-size: 24rpx;
  color: #999999;
}

.item-actions {
  display: flex;
  border-top: 1rpx solid #E6E6E6;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  font-size: 28rpx;
  transition: background-color 0.2s;
}

.action-btn:first-child {
  border-right: 1rpx solid #E6E6E6;
}

.redownload-btn {
  color: #1677FF;
}

.redownload-btn:active {
  background-color: #FFFFFF;
}

.delete-btn {
  color: #FF4D4F;
}

.delete-btn:active {
  background-color: #FFFFFF;
}

.action-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.action-text {
  font-size: 28rpx;
}

/* 底部工具栏 */
.bottom-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-top: 1rpx solid #E6E6E6;
  z-index: 100;
}

.toolbar-info {
  font-size: 28rpx;
  color: #666666;
}

.toolbar-btn {
  padding: 16rpx 32rpx;
  background-color: #FF4D4F;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
}

.toolbar-btn:active {
  opacity: 0.8;
}