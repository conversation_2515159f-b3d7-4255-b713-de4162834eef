# K12微信小程序云函数功能说明

## 概述

本文档详细说明K12微信小程序中所有云函数的功能、参数、返回值和使用场景。云函数采用微信云开发架构，基于Node.js运行时环境。

## 云函数统计表

| 序号 | 云函数名称 | 功能分类 | 主要作用 | 调用场景 |
|------|------------|----------|----------|----------|
| 1 | login | 用户认证 | 处理用户微信登录，获取用户信息 | 用户首次进入小程序 |
| 2 | user | 用户管理 | 用户信息的增删改查和积分管理 | 个人中心、积分查询 |
| 3 | material-getList | 资料管理 | 通用资料列表获取（基础稳定型） | 首页热门资料、多页面共用 |
| 4 | material-getFilteredList | 资料管理 | 专用筛选资料列表（专门优化型） | 热门资料筛选页面 |
| 5 | material-getFilterOptions | 资料管理 | 获取筛选器的可选项数据 | 筛选页面初始化 |
| 6 | getMaterialDetail | 资料管理 | 获取单个资料的详细信息 | 资料详情页面 |
| 7 | search-materials | 搜索功能 | 根据关键词搜索资料 | 搜索页面 |
| 8 | search-suggestions | 搜索功能 | 提供搜索关键词建议 | 搜索框实时建议 |
| 9 | search-hotKeywords | 搜索功能 | 获取热门搜索关键词 | 搜索页面热门搜索 |
| 10 | search-history | 搜索功能 | 用户搜索历史的增删改查 | 搜索页面历史记录 |
| 11 | favorite-add | 用户行为 | 添加或取消收藏资料 | 资料详情页、列表页 |
| 12 | resource | 系统管理 | 系统资源和配置管理 | 系统初始化、配置获取 |

**统计信息**：
- 总计：12个云函数
- 用户认证：1个
- 用户管理：1个  
- 资料管理：4个
- 搜索功能：4个
- 用户行为：1个
- 系统管理：1个

## 云函数列表

### 1. 用户认证相关

#### 1.1 login - 用户登录
- **功能**：处理用户微信登录，获取用户信息
- **路径**：`cloudfunctions/login/`
- **主要功能**：
  - 获取微信用户openid
  - 创建或更新用户信息
  - 返回用户登录状态
- **调用场景**：用户首次进入小程序或需要登录时

#### 1.2 user - 用户信息管理
- **功能**：用户信息的增删改查操作
- **路径**：`cloudfunctions/user/`
- **主要功能**：
  - 获取用户详细信息
  - 更新用户资料
  - 用户积分管理
- **调用场景**：个人中心、积分查询、资料更新

### 2. 资料管理相关

#### 2.1 material-getList - 通用资料列表获取
- **功能**：获取资料列表的通用云函数
- **路径**：`cloudfunctions/material-getList/`
- **输入参数**：
  ```javascript
  {
    grade: '',          // 年级筛选
    subject: '',        // 科目筛选
    volume: '',         // 册别筛选
    category: '',       // 分类筛选
    section: '',        // 板块筛选
    keyword: '',        // 关键词搜索
    page: 1,            // 页码
    pageSize: 20,       // 每页数量
    sortType: 'download', // 排序类型
    sortBy: '',         // 兼容参数
    sortOrder: 'desc',  // 排序方向
    excludeUpgrade: false // 是否排除升学专区
  }
  ```
- **返回数据**：
  ```javascript
  {
    success: true,
    data: [...],        // 资料列表
    total: 156,         // 总数量
    page: 1,            // 当前页码
    pageSize: 20,       // 每页数量
    hasMore: true,      // 是否有更多数据
    message: '获取成功'
  }
  ```
- **调用场景**：首页热门资料、分类页面、API封装层
- **特点**：稳定的基础功能，多页面共用

#### 2.2 material-getFilteredList - 专用筛选资料列表
- **功能**：专门为筛选页面优化的资料列表获取
- **路径**：`cloudfunctions/material-getFilteredList/`
- **输入参数**：与`material-getList`相同
- **返回数据**：
  ```javascript
  {
    success: true,
    data: [...],
    total: 156,
    page: 1,
    pageSize: 20,
    hasMore: true,
    message: '筛选获取成功',
    filterInfo: {       // 额外的筛选信息
      grade: '三年级',
      subject: '数学',
      volume: '上册',
      section: '计算',
      keyword: ''
    }
  }
  ```
- **调用场景**：热门资料筛选页面
- **特点**：
  - 包含详细的调试日志
  - 专门优化筛选逻辑
  - 支持复杂筛选条件组合
  - 独立维护，不影响其他功能

#### 2.3 material-getFilterOptions - 筛选选项获取
- **功能**：获取筛选器的可选项数据
- **路径**：`cloudfunctions/material-getFilterOptions/`
- **输入参数**：无
- **返回数据**：
  ```javascript
  {
    success: true,
    data: {
      grades: [...],    // 年级选项
      subjects: [...],  // 科目选项
      volumes: [...],   // 册别选项
      sections: [...]   // 板块选项
    },
    message: '获取成功'
  }
  ```
- **调用场景**：筛选页面初始化时加载筛选选项
- **特点**：动态获取数据库中的实际选项值

#### 2.4 getMaterialDetail - 资料详情获取
- **功能**：获取单个资料的详细信息
- **路径**：`cloudfunctions/getMaterialDetail/`
- **输入参数**：
  ```javascript
  {
    id: 'material_id'   // 资料ID
  }
  ```
- **返回数据**：资料的完整详细信息
- **调用场景**：资料详情页面

### 3. 搜索相关

#### 3.1 search-materials - 资料搜索
- **功能**：根据关键词搜索资料
- **路径**：`cloudfunctions/search-materials/`
- **主要功能**：
  - 全文搜索资料标题
  - 支持模糊匹配
  - 搜索结果排序
- **调用场景**：搜索页面

#### 3.2 search-suggestions - 搜索建议
- **功能**：提供搜索关键词建议
- **路径**：`cloudfunctions/search-suggestions/`
- **主要功能**：
  - 根据输入提供搜索建议
  - 智能匹配相关关键词
- **调用场景**：搜索框输入时的实时建议

#### 3.3 search-hotKeywords - 热门搜索关键词
- **功能**：获取热门搜索关键词
- **路径**：`cloudfunctions/search-hotKeywords/`
- **主要功能**：
  - 统计热门搜索词
  - 返回排序后的热门关键词列表
- **调用场景**：搜索页面显示热门搜索

#### 3.4 search-history - 搜索历史管理
- **功能**：用户搜索历史的增删改查
- **路径**：`cloudfunctions/search-history/`
- **主要功能**：
  - 保存用户搜索记录
  - 获取搜索历史
  - 清除搜索历史
- **调用场景**：搜索页面的历史记录功能

### 4. 用户行为相关

#### 4.1 favorite-add - 收藏功能
- **功能**：添加或取消收藏资料
- **路径**：`cloudfunctions/favorite-add/`
- **主要功能**：
  - 添加资料到收藏夹
  - 取消收藏
  - 检查收藏状态
- **调用场景**：资料详情页面、资料列表页面

### 5. 系统资源相关

#### 5.1 resource - 系统资源管理
- **功能**：系统资源和配置管理
- **路径**：`cloudfunctions/resource/`
- **主要功能**：
  - 获取系统配置
  - 资源文件管理
  - 系统参数配置
- **调用场景**：系统初始化、配置获取

## 云函数架构设计

### 设计原则

1. **功能拆分原则**：
   - 不同业务场景使用独立的云函数
   - 避免单一云函数承担过多职责
   - 降低功能间的耦合度

2. **稳定性原则**：
   - 核心功能使用稳定的云函数（如`material-getList`）
   - 新功能使用独立的云函数（如`material-getFilteredList`）
   - 避免频繁修改影响现有功能

3. **可扩展性原则**：
   - 每个云函数职责明确
   - 便于独立优化和维护
   - 支持功能的独立迭代

### 函数分类

#### 基础稳定型
- `material-getList`：多页面共用的基础资料获取
- `login`：用户登录基础功能
- `user`：用户信息基础管理

#### 专用优化型
- `material-getFilteredList`：专门为筛选页面优化
- `search-materials`：专门的搜索功能
- `material-getFilterOptions`：专门的筛选选项获取

#### 功能扩展型
- `favorite-add`：收藏功能扩展
- `search-history`：搜索历史扩展
- `resource`：系统资源扩展

## 部署和维护

### 部署方式
- 使用微信开发者工具进行云函数部署
- 支持云端安装依赖
- 使用腾讯云开发控制台进行管理

### 监控和日志
- 每个云函数包含详细的console.log日志
- 可通过云开发控制台查看函数执行日志
- 支持错误监控和性能分析

### 版本管理
- 采用功能拆分避免版本冲突
- 新功能使用独立云函数
- 保持向后兼容性

## 最佳实践

1. **错误处理**：
   - 所有云函数都包含try-catch错误处理
   - 返回统一的错误格式
   - 提供降级方案（如模拟数据）

2. **参数验证**：
   - 对输入参数进行验证和默认值设置
   - 支持向后兼容的参数格式

3. **性能优化**：
   - 合理使用数据库索引
   - 控制返回数据的字段和数量
   - 实现分页加载

4. **安全性**：
   - 验证用户权限
   - 过滤敏感数据
   - 防止SQL注入等安全问题

## 更新历史

- **2024年**：初始版本，包含基础的资料管理和用户功能
- **最近更新**：
  - 新增`material-getFilteredList`专用筛选云函数
  - 修复筛选器字段名不匹配问题
  - 优化筛选逻辑，支持复杂筛选条件
  - 采用功能拆分架构，提高系统稳定性

---

*文档最后更新时间：2025年1月*