// cloudfunctions/user-getStats/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { userId } = event
  
  try {
    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      }
    }

    // 并行查询用户的各项统计数据
    const [downloadResult, favoriteResult, shareResult] = await Promise.allSettled([
      // 查询下载数量
      db.collection('downloads').where({
        userId: userId
      }).count(),
      
      // 查询收藏数量
      db.collection('favorites').where({
        userId: userId
      }).count(),
      
      // 查询分享数量（如果有分享记录表的话）
      db.collection('shares').where({
        userId: userId
      }).count().catch(() => ({ total: 0 })) // 如果表不存在，返回0
    ])

    // 处理查询结果
    const downloadCount = downloadResult.status === 'fulfilled' ? downloadResult.value.total : 0
    const favoriteCount = favoriteResult.status === 'fulfilled' ? favoriteResult.value.total : 0
    const shareCount = shareResult.status === 'fulfilled' ? shareResult.value.total : 0

    return {
      success: true,
      data: {
        downloadCount,
        favoriteCount,
        shareCount
      }
    }
    
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return {
      success: false,
      message: '获取用户统计失败',
      error: error.message
    }
  }
}