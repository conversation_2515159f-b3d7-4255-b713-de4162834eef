// pages/feedback/feedback.js
const app = getApp()

Page({
  data: {
    feedbackType: 'bug', // bug: 问题反馈, suggestion: 建议, other: 其他
    feedbackTypes: [
      { value: 'bug', label: '问题反馈' },
      { value: 'suggestion', label: '功能建议' },
      { value: 'content', label: '内容问题' },
      { value: 'other', label: '其他' }
    ],
    currentTypeLabel: '问题反馈', // 当前选中的类型标签
    title: '',
    content: '',
    contact: '',
    images: [],
    maxImages: 3,
    submitting: false
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 选择反馈类型
  onTypeChange(e) {
    const typeIndex = e.detail.value
    const selectedType = this.data.feedbackTypes[typeIndex]
    this.setData({
      feedbackType: selectedType.value,
      currentTypeLabel: selectedType.label
    })
  },

  // 输入标题
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    })
  },

  // 输入内容
  onContentInput(e) {
    this.setData({
      content: e.detail.value
    })
  },

  // 输入联系方式
  onContactInput(e) {
    this.setData({
      contact: e.detail.value
    })
  },

  // 选择图片
  chooseImage() {
    const remainingCount = this.data.maxImages - this.data.images.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多上传${this.data.maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = [...this.data.images, ...res.tempFilePaths]
        this.setData({
          images: newImages
        })
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset
    wx.previewImage({
      current: this.data.images[index],
      urls: this.data.images
    })
  },

  // 删除图片
  deleteImage(e) {
    const { index } = e.currentTarget.dataset
    const images = [...this.data.images]
    images.splice(index, 1)
    this.setData({ images })
  },

  // 提交反馈
  submitFeedback() {
    // 验证表单
    if (!this.validateForm()) {
      return
    }

    this.setData({ submitting: true })

    // 构建反馈数据
    const feedbackData = {
      type: this.data.feedbackType,
      title: this.data.title.trim(),
      content: this.data.content.trim(),
      contact: this.data.contact.trim(),
      images: this.data.images,
      createTime: new Date().toISOString(),
      userInfo: wx.getStorageSync('userInfo') || {}
    }

    // 模拟提交API调用
    this.performSubmit(feedbackData)
  },

  // 验证表单
  validateForm() {
    if (!this.data.title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      })
      return false
    }

    if (this.data.title.trim().length < 5) {
      wx.showToast({
        title: '标题至少5个字符',
        icon: 'none'
      })
      return false
    }

    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return false
    }

    if (this.data.content.trim().length < 10) {
      wx.showToast({
        title: '内容至少10个字符',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 执行提交
  async performSubmit(feedbackData) {
    try {
      // 调用云函数提交反馈
      try {
        const result = await app.api.feedback.submitFeedback(feedbackData)
        
        if (result.success) {
          // 云函数提交成功
          this.setData({ submitting: false })
          
          wx.showToast({
            title: '提交成功，获得20积分',
            icon: 'success',
            duration: 2000
          })
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 2000)
        } else {
          throw new Error(result.message || '提交失败')
        }
      } catch (cloudError) {
        console.error('云函数提交失败，使用降级方案:', cloudError)
        
        // 降级到本地存储
        const feedbacks = wx.getStorageSync('feedbacks') || []
        feedbacks.unshift({
          ...feedbackData,
          id: Date.now().toString(),
          status: 'pending' // pending: 待处理, processing: 处理中, resolved: 已解决
        })
        wx.setStorageSync('feedbacks', feedbacks)
        
        // 给用户积分奖励
        const currentPoints = app.getUserPoints()
        app.updateUserPoints(currentPoints + 20)
        
        this.setData({ submitting: false })
        
        wx.showToast({
          title: '提交成功，获得20积分',
          icon: 'success',
          duration: 2000
        })
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      }
      
    } catch (error) {
      console.error('提交反馈失败:', error)
      this.setData({ submitting: false })
      
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    }
  },

  // 重置表单
  resetForm() {
    wx.showModal({
      title: '确认重置',
      content: '确定要清空所有内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            feedbackType: 'bug',
            title: '',
            content: '',
            contact: '',
            images: []
          })
          
          wx.showToast({
            title: '已重置',
            icon: 'success'
          })
        }
      }
    })
  },

  // 查看反馈历史
  viewHistory() {
    const feedbacks = wx.getStorageSync('feedbacks') || []
    if (feedbacks.length === 0) {
      wx.showToast({
        title: '暂无反馈记录',
        icon: 'none'
      })
      return
    }

    // 显示反馈历史列表
    const historyList = feedbacks.map((item, index) => {
      const statusText = {
        pending: '待处理',
        processing: '处理中',
        resolved: '已解决'
      }
      return `${index + 1}. ${item.title} (${statusText[item.status]})`
    })

    wx.showActionSheet({
      itemList: historyList.slice(0, 6), // 最多显示6条
      success: (res) => {
        const selectedFeedback = feedbacks[res.tapIndex]
        this.showFeedbackDetail(selectedFeedback)
      }
    })
  },

  // 显示反馈详情
  showFeedbackDetail(feedback) {
    const statusText = {
      pending: '待处理',
      processing: '处理中',
      resolved: '已解决'
    }

    wx.showModal({
      title: feedback.title,
      content: `状态：${statusText[feedback.status]}\n内容：${feedback.content}\n提交时间：${new Date(feedback.createTime).toLocaleString()}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '意见反馈 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})