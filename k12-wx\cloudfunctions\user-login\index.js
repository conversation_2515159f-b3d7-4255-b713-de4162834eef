// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { userInfo } = event
  
  try {
    // 获取用户openid
    const openid = wxContext.OPENID
    
    if (!openid) {
      return {
        success: false,
        message: '获取用户身份失败'
      }
    }
    
    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()
    
    let isNewUser = false
    let user = null
    
    if (userResult.data && userResult.data.length > 0) {
      // 用户已存在，返回用户信息
      user = userResult.data[0]
      
      // 更新用户信息（如果有传入）
      if (userInfo) {
        await db.collection('users').doc(user._id).update({
          data: {
            ...userInfo,
            lastLoginTime: new Date(),
            updateTime: new Date()
          }
        })
        
        user = {
          ...user,
          ...userInfo,
          lastLoginTime: new Date(),
          updateTime: new Date()
        }
      }
      
    } else {
      // 新用户，创建用户记录并发放200积分
      isNewUser = true
      
      const newUserData = {
        openid: openid,
        points: 200, // 新用户默认200积分
        createTime: new Date(),
        lastLoginTime: new Date(),
        updateTime: new Date(),
        ...userInfo
      }
      
      // 创建用户记录
      const createResult = await db.collection('users').add({
        data: newUserData
      })
      
      user = {
        _id: createResult._id,
        ...newUserData
      }
      
      // 记录积分历史
      await db.collection('points_history').add({
        data: {
          openid: openid,
          action: 'newUser',
          points: 200,
          description: '新用户注册奖励',
          date: new Date().toISOString().split('T')[0],
          createTime: new Date(),
          extra: {}
        }
      })
    }
    
    return {
      success: true,
      data: {
        user: user,
        isNewUser: isNewUser
      },
      message: isNewUser ? '注册成功，获得200积分新用户奖励！' : '登录成功'
    }
    
  } catch (error) {
    console.error('用户登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message
    }
  }
}