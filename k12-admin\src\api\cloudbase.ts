import cloudbase from '@cloudbase/js-sdk';

const envId = import.meta.env.VITE_TCB_ENV_ID || 'cloud1-8gm001v7fd56ff43';

// 全局状态管理
declare global {
  interface Window {
    __tcb_app__?: any;
    __tcb_auth_state__?: {
      isLoggedIn: boolean;
      loginPromise?: Promise<any>;
    };
    __tcb_module_id__?: string;
  }
}

// 生成唯一的模块ID，用于检测模块重载
const MODULE_ID = Date.now().toString();

// 清理之前的实例（处理HMR重载）
if (window.__tcb_module_id__ && window.__tcb_module_id__ !== MODULE_ID) {
  console.log('检测到模块重载，清理之前的云开发实例');
  window.__tcb_app__ = undefined;
  window.__tcb_auth_state__ = undefined;
}
window.__tcb_module_id__ = MODULE_ID;

function getApp() {
  // 检查是否已有实例
  if (window.__tcb_app__) {
    return window.__tcb_app__;
  }
  
  // 创建新实例
  console.log('初始化云开发SDK，环境ID:', envId);
  const app = cloudbase.init({
    env: envId
  });
  
  // 存储到全局
  window.__tcb_app__ = app;
  
  return app;
}

// 确保认证状态
async function ensureAuth() {
  const app = getApp();
  
  // 初始化全局认证状态
  if (!window.__tcb_auth_state__) {
    window.__tcb_auth_state__ = {
      isLoggedIn: false
    };
  }
  
  // 如果已经登录，直接返回
  if (window.__tcb_auth_state__.isLoggedIn) {
    return;
  }
  
  // 如果有登录Promise在进行中，等待它完成
  if (window.__tcb_auth_state__.loginPromise) {
    await window.__tcb_auth_state__.loginPromise;
    return;
  }
  
  const auth = app.auth();
  
  try {
    // 检查当前登录状态
    const loginState = await auth.getLoginState();
    if (loginState && loginState.loginType) {
      console.log('当前已登录:', loginState.loginType);
      window.__tcb_auth_state__.isLoggedIn = true;
      return;
    }
  } catch (error) {
    console.log('获取登录状态失败:', error);
  }
  
  // 如果没有登录状态，进行匿名登录
  console.log('当前无登录状态，开始匿名登录...');
  window.__tcb_auth_state__.loginPromise = auth.signInAnonymously();
  
  try {
    const result = await window.__tcb_auth_state__.loginPromise;
    console.log('匿名登录成功:', result);
    window.__tcb_auth_state__.isLoggedIn = true;
  } catch (error) {
    console.error('匿名登录失败:', error);
    // 即使认证失败，也尝试继续执行，因为安全规则已设置为READONLY
    console.log('尝试以只读模式继续...');
  } finally {
    window.__tcb_auth_state__.loginPromise = undefined;
  }
}

// 获取数据库引用
function getDb() {
  return getApp().database();
}

/**
 * 获取文件列表
 */
export async function getFileList(query: string = '{}', limit: number = 10, offset: number = 0) {
  try {
    await ensureAuth();
    
    const db = getDb();
    let dbQuery = db.collection('files');
    
    // 解析查询条件
    if (query && query !== '{}') {
      try {
        const queryObj = JSON.parse(query);
        Object.keys(queryObj).forEach(key => {
          dbQuery = dbQuery.where({
            [key]: queryObj[key]
          });
        });
      } catch (parseError) {
        console.warn('查询条件解析失败，使用默认查询:', parseError);
      }
    }
    
    const result = await dbQuery
      .orderBy('createTime', 'desc')
      .limit(limit)
      .skip(offset)
      .get();
    
    return {
      code: 200,
      message: '获取列表成功',
      Data: result.data.map((item: any) => JSON.stringify(item)),
      data: result.data,
      total: result.data.length
    };
  } catch (error: any) {
    console.error('获取文件列表失败:', error);
    return {
      code: 500,
      message: '获取文件列表失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 直接上传文件到云存储
 */
export async function uploadFileToStorage(file: File, cloudPath: string) {
  try {
    await ensureAuth();
    
    if (!file || !cloudPath) {
      return {
        code: 400,
        message: '缺少文件或cloudPath参数'
      };
    }

    const app = getApp();
    const result = await app.uploadFile({
      cloudPath: cloudPath,
      filePath: file
    });

    return {
      code: 200,
      message: '上传成功',
      data: result
    };
  } catch (error: any) {
    console.error('文件上传失败:', error);
    return {
      code: 500,
      message: '文件上传失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 获取上传URL（保持兼容性）
 */
export async function getUploadUrl(cloudPath: string) {
  try {
    await ensureAuth();
    
    if (!cloudPath) {
      return {
        code: 400,
        message: '缺少 cloudPath 参数'
      };
    }

    return {
      code: 200,
      message: '获取成功',
      data: {
        cloudPath: cloudPath,
        uploadUrl: cloudPath
      }
    };
  } catch (error: any) {
    console.error('获取上传URL失败:', error);
    return {
      code: 500,
      message: '获取上传URL失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 创建文件记录
 */
export async function createFileRecord(file: any) {
  try {
    await ensureAuth();
    
    if (!file || !file.fileID) {
      return {
        code: 400,
        message: '缺少文件(file)信息'
      };
    }

    const db = getDb();
    const result = await db.collection('files').add({
      ...file,
      createTime: new Date()
    });

    return {
      code: 200,
      message: '创建成功',
      data: result
    };
  } catch (error: any) {
    console.error('创建文件记录失败:', error);
    return {
      code: 500,
      message: '创建文件记录失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 删除文件（同时删除云存储文件和数据库记录）
 */
export async function deleteFile(fileId: string, cloudPath: string) {
  try {
    await ensureAuth();
    
    if (!fileId) {
      return {
        code: 400,
        message: '缺少文件ID参数'
      };
    }

    const app = getApp();
    const db = getDb();

    console.log('开始删除文件，fileId:', fileId, 'cloudPath:', cloudPath);

    // 1. 先获取文件详情，确保有正确的fileID用于云存储删除
    let fileToDelete = cloudPath;
    try {
      const fileDetail = await db.collection('files').doc(fileId).get();
      if (fileDetail.data && fileDetail.data.length > 0) {
        const fileData = fileDetail.data[0];
        // 优先使用fileID，如果没有则使用cloudPath
        fileToDelete = fileData.fileID || fileData.cloudPath || cloudPath;
        console.log('从数据库获取到的文件信息:', {
          fileID: fileData.fileID,
          cloudPath: fileData.cloudPath,
          willDelete: fileToDelete
        });
      }
    } catch (dbError) {
      console.warn('获取文件详情失败，使用传入的cloudPath:', dbError);
    }

    // 2. 删除云存储中的文件
    if (fileToDelete) {
      try {
        console.log('正在删除云存储文件:', fileToDelete);
        const deleteResult = await app.deleteFile({
          fileList: [fileToDelete]
        });
        console.log('云存储文件删除结果:', deleteResult);
        
        // 检查删除结果
        if (deleteResult && deleteResult.fileList) {
          const deletedFile = deleteResult.fileList[0];
          if (deletedFile && deletedFile.code === 'SUCCESS') {
            console.log('云存储文件删除成功:', fileToDelete);
          } else {
            console.warn('云存储文件删除可能失败:', deletedFile);
          }
        }
      } catch (storageError) {
        console.error('云存储文件删除失败:', storageError);
        // 不要因为云存储删除失败就停止整个流程，继续删除数据库记录
      }
    } else {
      console.warn('没有找到要删除的云存储文件路径');
    }

    // 3. 删除数据库记录
    console.log('正在删除数据库记录:', fileId);
    const result = await db.collection('files').doc(fileId).remove();
    console.log('数据库记录删除结果:', result);

    return {
      code: 200,
      message: '删除成功',
      data: result
    };
  } catch (error: any) {
    console.error('删除文件失败:', error);
    return {
      code: 500,
      message: '删除文件失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 更新文件记录
 */
export async function updateFileRecord(fileId: string, updateData: any) {
  try {
    await ensureAuth();
    
    if (!fileId || !updateData) {
      return {
        code: 400,
        message: '缺少文件ID或更新数据'
      };
    }

    const db = getDb();
    const result = await db.collection('files').doc(fileId).update({
      ...updateData,
      updateTime: new Date()
    });

    return {
      code: 200,
      message: '更新成功',
      data: result
    };
  } catch (error: any) {
    console.error('更新文件记录失败:', error);
    return {
      code: 500,
      message: '更新文件记录失败',
      error: error?.message || '未知错误'
    };
  }
}

/**
 * 根据ID获取单个文件详情
 */
export async function getFileById(fileId: string) {
  try {
    await ensureAuth();
    
    if (!fileId) {
      return {
        code: 400,
        message: '缺少文件ID参数'
      };
    }

    const db = getDb();
    const result = await db.collection('files').doc(fileId).get();

    if (!result.data || result.data.length === 0) {
      return {
        code: 404,
        message: '文件不存在'
      };
    }

    return {
      code: 200,
      message: '获取成功',
      data: result.data[0]
    };
  } catch (error: any) {
    console.error('获取文件详情失败:', error);
    return {
      code: 500,
      message: '获取文件详情失败',
      error: error?.message || '未知错误'
    };
  }
}

// 通用云开发请求函数
export const cloudbaseRequest = async (options: {
  collection: string
  action: string
  data?: any
}) => {
  try {
    await ensureAuth()
    const db = getDb()
    
    const { collection, action, data = {} } = options
    
    // 根据action执行不同的数据库操作
    switch (action) {
      case 'list':
        const pageSize = parseInt(data.pageSize) || 20
        const page = parseInt(data.page) || 1
        const skipCount = (page - 1) * pageSize
        
        // 积分规则从数据库读取，如果没有数据则初始化默认规则
        if (collection === 'pointRules') {
          try {
            let dbQuery = db.collection(collection)
            
            // 根据type过滤
            if (data.type) {
              dbQuery = dbQuery.where({
                type: data.type
              })
            }
            
            const result = await dbQuery
              .orderBy('createTime', 'desc')
              .limit(pageSize)
              .skip(skipCount)
              .get()
            
            // 如果数据库中没有数据，初始化默认规则
            if (!result.data || result.data.length === 0) {
              const defaultRules = [
                {
                  type: 'earn',
                  action: 'register',
                  name: '用户注册',
                  description: '新用户注册奖励',
                  points: 200,
                  dailyLimit: 1,
                  status: 'active',
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                },
                {
                  type: 'earn',
                  action: 'checkin',
                  name: '每日签到',
                  description: '每日签到奖励',
                  points: 50,
                  dailyLimit: 1,
                  status: 'active',
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                },
                {
                  type: 'earn',
                  action: 'share',
                  name: '分享文件',
                  description: '分享文件奖励',
                  points: 10,
                  dailyLimit: 10,
                  status: 'active',
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                },
                {
                  type: 'consume',
                  action: 'download',
                  name: '下载文件',
                  description: '下载文件消费积分',
                  points: 20,
                  status: 'active',
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                },
                {
                  type: 'limit',
                  action: 'daily_earn',
                  name: '每日获得上限',
                  description: '每日最多获得500积分',
                  points: 500,
                  status: 'active',
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                }
              ]
              
              // 批量插入默认规则
              const insertPromises = defaultRules.map(rule => 
                db.collection(collection).add(rule)
              )
              await Promise.all(insertPromises)
              
              // 根据type过滤默认规则
              let filteredRules = defaultRules
              if (data.type) {
                filteredRules = filteredRules.filter(rule => rule.type === data.type)
              }
              
              return {
                success: true,
                data: filteredRules,
                total: filteredRules.length
              }
            }
            
            return {
              success: true,
              data: result.data,
              total: result.data.length
            }
          } catch (error) {
            console.error('获取积分规则失败:', error)
            return {
              success: false,
              message: '获取积分规则失败',
              error
            }
          }
        }
        
        // 系统配置返回模拟数据
        if (collection === 'systemConfig') {
          return {
            success: true,
            data: {
              key: data.key || 'pointsConfig',
              value: {
                antiCheatEnabled: true,
                antiCheatInterval: 60,
                maxDailyEarn: 500,
                maxSingleEarn: 100
              },
              updateTime: new Date().toISOString()
            }
          }
        }
        
        // 如果是用户列表，返回模拟数据（基于微信登录的用户数据结构）
        if (collection === 'users') {
          const mockUsers = [
            {
              _id: '1',
              openid: 'openid_001',
              nickname: '张三',
              avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLL1byctY955FriaEx5wKCkzEGOlnZ1LCherEakjjof8ZS8qJRtdzhvs2aldSUIcGj9vb0I8ibmicFrA/132',
              points: 1200,
              total_points_earned: 1500,
              total_points_spent: 300,
              download_count: 45,
              favorite_count: 12,
              share_count: 8,
              status: 'active',
              created_time: '2024-01-15T08:30:00Z',
              last_login_time: '2024-01-20T14:25:00Z',
              updated_time: '2024-01-20T14:25:00Z'
            },
            {
              _id: '2',
              openid: 'openid_002',
              nickname: '李四',
              avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eoj0hHXhgJNOTSOFsS4uZs8x1ConecaVOB8eIl115xmJZcT4oCicvia7wMEufibKtTLqmXSoXBZjcLRw/132',
              points: 800,
              total_points_earned: 1000,
              total_points_spent: 200,
              download_count: 28,
              favorite_count: 8,
              share_count: 5,
              status: 'active',
              created_time: '2024-01-10T10:15:00Z',
              last_login_time: '2024-01-19T16:40:00Z',
              updated_time: '2024-01-19T16:40:00Z'
            },
            {
              _id: '3',
              openid: 'openid_003',
              nickname: '王五',
              avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4uJ3Hk4UESX2oqkekiSFPabMQ4AA3AYLqjHH1zA6dZZoOBYeNOh2BIeP1RCq7eTn9QtW5Oe2Gga/132',
              points: 200,
              total_points_earned: 300,
              total_points_spent: 100,
              download_count: 15,
              favorite_count: 3,
              share_count: 2,
              status: 'banned',
              created_time: '2024-01-05T12:20:00Z',
              last_login_time: '2024-01-18T09:15:00Z',
              updated_time: '2024-01-18T09:15:00Z'
            },
            {
              _id: '4',
              openid: 'openid_004',
              nickname: '赵六',
              avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/ajNVdqHZLLBWJrzQpkRCBNadAoRoKWTjMIibteeX8opHajOnrv0VLs8YMChOgRcXfZqVZVntH2fKk2sOfVQBNEA/132',
              points: 500,
              total_points_earned: 600,
              total_points_spent: 100,
              download_count: 20,
              favorite_count: 5,
              share_count: 3,
              status: 'active',
              created_time: '2024-01-08T14:20:00Z',
              last_login_time: '2024-01-19T10:30:00Z',
              updated_time: '2024-01-19T10:30:00Z'
            },
            {
              _id: '5',
              openid: 'openid_005',
              nickname: '孙七',
              avatar_url: 'https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKcgSE3rdwtMakrC3fQKFrMiq4r3op0pC5RqWehWGVQrG3CzxmRdB5qflq40tgTg2qKENhwgAXiaw/132',
              points: 150,
              total_points_earned: 200,
              total_points_spent: 50,
              download_count: 8,
              favorite_count: 2,
              share_count: 1,
              status: 'inactive',
              created_time: '2024-01-12T16:45:00Z',
              last_login_time: '2024-01-15T11:20:00Z',
              updated_time: '2024-01-15T11:20:00Z'
            }
          ]
          
          // 应用搜索过滤
          let filteredUsers = mockUsers
          if (data.keyword) {
            filteredUsers = filteredUsers.filter(user => 
              user.nickname.includes(data.keyword)
            )
          }
          if (data.status) {
            filteredUsers = filteredUsers.filter(user => 
              user.status === data.status
            )
          }
          
          // 分页
          const startIndex = skipCount
          const endIndex = startIndex + pageSize
          const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
          
          return {
            success: true,
            data: paginatedUsers,
            total: filteredUsers.length
          }
        }
        
        const listResult = await db.collection(collection)
          .limit(pageSize)
          .skip(skipCount)
          .get()
        return {
          success: true,
          data: listResult.data,
          total: listResult.data.length
        }
      
      case 'get':
        // 处理系统配置获取
        if (collection === 'systemConfig') {
          const configKey = data.key || data._id
          if (configKey === 'pointsConfig') {
            return {
              success: true,
              data: {
                key: 'pointsConfig',
                value: {
                  antiCheatEnabled: true,
                  antiCheatInterval: 60,
                  maxDailyEarn: 500,
                  maxSingleEarn: 100
                },
                updateTime: new Date().toISOString()
              }
            }
          }
          if (configKey === 'antiCheat') {
            return {
              success: true,
              data: {
                enabled: true,
                maxDailyEarn: 500,
                maxSingleEarn: 100,
                suspiciousThreshold: 10,
                autoBlock: false,
                whitelist: []
              }
            }
          }
        }
        
        // 确保_id存在且为有效值
        if (!data._id || (typeof data._id !== 'string' && typeof data._id !== 'number')) {
          return {
            success: false,
            message: '无效的文档ID'
          }
        }
        
        const getResult = await db.collection(collection).doc(data._id).get()
        return {
          success: true,
          data: getResult.data && getResult.data.length > 0 ? getResult.data[0] : null
        }
      
      case 'create':
        const createResult = await db.collection(collection).add(data)
        return {
          success: true,
          data: createResult
        }
      
      case 'update':
        const updateResult = await db.collection(collection).doc(data._id).update(data)
        return {
          success: true,
          data: updateResult
        }
      
      case 'delete':
        const deleteResult = await db.collection(collection).doc(data._id).remove()
        return {
          success: true,
          data: deleteResult
        }
      
      case 'batchDelete':
        // 检查是否有ids数组
        if (!data.ids || !Array.isArray(data.ids)) {
          return {
            success: false,
            message: '缺少有效的ids数组'
          }
        }
        const batchDeletePromises = data.ids.map((id: string) => 
          db.collection(collection).doc(id).remove()
        )
        await Promise.all(batchDeletePromises)
        return {
          success: true,
          message: '批量删除成功'
        }
      
      case 'batchUpdate':
        // 处理积分规则的批量保存
        if (collection === 'pointRules' && data.rules && Array.isArray(data.rules)) {
          console.log('开始批量更新积分规则:', data.rules)
          
          try {
            const updateResults = []
            
            for (const rule of data.rules) {
              console.log('处理规则:', rule)
              
              if (rule._id) {
                // 更新现有规则
                console.log('更新现有规则:', rule._id)
                const updateResult = await db.collection(collection).doc(rule._id).update({
                  ...rule,
                  updateTime: new Date().toISOString()
                })
                updateResults.push({ action: 'update', id: rule._id, result: updateResult })
              } else {
                // 创建新规则
                console.log('创建新规则:', rule)
                const createResult = await db.collection(collection).add({
                  ...rule,
                  createTime: new Date().toISOString(),
                  updateTime: new Date().toISOString()
                })
                updateResults.push({ action: 'create', result: createResult })
              }
            }
            
            console.log('批量更新完成，结果:', updateResults)
            
            return {
              success: true,
              message: '积分规则保存成功',
              data: updateResults
            }
          } catch (error) {
            console.error('批量更新积分规则失败:', error)
            return {
              success: false,
              message: '积分规则保存失败: ' + (error as any)?.message,
              error
            }
          }
        }
        
        // 检查是否有ids数组（原有的批量更新逻辑）
        if (!data.ids || !Array.isArray(data.ids)) {
          return {
            success: false,
            message: '缺少有效的ids数组'
          }
        }
        const batchUpdatePromises = data.ids.map((id: string) => 
          db.collection(collection).doc(id).update({ status: data.status })
        )
        await Promise.all(batchUpdatePromises)
        return {
          success: true,
          message: '批量更新成功'
        }
      
      case 'stats':
        // 返回模拟统计数据
        if (collection === 'users') {
          return {
            success: true,
            data: {
              totalUsers: 1250,
              activeUsers: 980,
              newUsersToday: 25,
              bannedUsers: 15,
              inactiveUsers: 255,
              levelDistribution: [
                { level: 1, count: 500 },
                { level: 2, count: 300 },
                { level: 3, count: 200 },
                { level: 4, count: 150 },
                { level: 5, count: 100 }
              ],
              registrationTrend: [
                { date: '2024-01-01', count: 50 },
                { date: '2024-01-02', count: 45 },
                { date: '2024-01-03', count: 60 }
              ]
            }
          }
        }
        return {
          success: true,
          data: {
            totalRules: 10,
            activeRules: 8,
            totalRecords: 1000,
            todayRecords: 50
          }
        }
      
      default:
        return {
          success: true,
          data: {},
          message: '操作成功'
        }
    }
  } catch (error: any) {
    console.error('云开发请求失败:', error)
    return {
      success: false,
      message: error?.message || '请求失败',
      error
    }
  }
}

// 导出实例获取函数
export { getApp as app, getDb as db };