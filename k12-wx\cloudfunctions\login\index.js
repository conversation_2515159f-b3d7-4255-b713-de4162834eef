// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { code, userInfo } = event
  
  try {
    // 获取微信用户信息
    const { result } = await cloud.openapi.sns.jscode2session({
      appid: 'wxdcb01784f343322b', // 你的小程序appid
      secret: 'your-app-secret', // 你的小程序secret
      jsCode: code,
      grantType: 'authorization_code'
    })
    
    const { openid, unionid, session_key } = result
    
    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    let userData
    
    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      const createResult = await db.collection('users').add({
        data: {
          openid: openid,
          unionid: unionid,
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          points: 100, // 新用户赠送100积分
          createTime: new Date(),
          lastLoginTime: new Date(),
          downloadCount: 0,
          favoriteCount: 0
        }
      })
      
      userData = {
        _id: createResult._id,
        openid: openid,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        points: 100,
        isNewUser: true
      }
      
    } else {
      // 老用户，更新登录时间
      const user = userQuery.data[0]
      await db.collection('users').doc(user._id).update({
        data: {
          lastLoginTime: new Date(),
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl
        }
      })
      
      userData = {
        _id: user._id,
        openid: user.openid,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        points: user.points,
        isNewUser: false
      }
    }
    
    return {
      success: true,
      data: userData,
      message: '登录成功'
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message
    }
  }
}