# 升学专区文件上传操作说明

## 分类配置

升学专区使用固定分类，直接在数据库中存储中文名称：

### 幼升小 (upgradeType: kindergarten)
- 拼音启蒙
- 认识数字  
- 习惯养成
- 学科启蒙
- 知识科普

### 小升初 (upgradeType: primary)
- 语文冲刺
- 数学冲刺
- 英语强化
- 面试准备
- 真题模拟

## 数据库字段

上传文件到 `files` 集合时，需要包含以下字段：

```json
{
  "title": "文件标题",
  "description": "文件描述", 
  "upgradeType": "kindergarten" | "primary",
  "upgradeCategory": "拼音启蒙" | "语文冲刺" | ...,
  "subject": "语文" | "数学" | "英语" | "综合" | "科学",
  "tags": ["标签1", "标签2"],
  "points": 积分数值,
  "fileSize": "文件大小",
  "fileType": "文件类型",
  "downloadCount": 下载次数,
  "viewCount": 查看次数,
  "uploadTime": "上传时间",
  "isRecommended": true/false,
  "category": null,
  "grade": null
}
```

**注意：** 
- 升学专区文件的 `category` 和 `grade` 字段可以为空，因为使用 `upgradeType` 和 `upgradeCategory` 进行分类
- `isRecommended` 字段目前在小程序中未使用，可以预留用于后续推荐功能开发
- 如果不需要推荐功能，可以省略 `isRecommended` 字段

## 操作步骤

1. 选择升学类型：幼升小 或 小升初
2. 选择对应的固定分类
3. 填写文件信息
4. 上传文件
5. 设置积分和推荐状态

## 后台代码设计

当用户选择升学专区按钮时，直接给出对应的字段值：

### 幼升小按钮
```javascript
// 用户点击"拼音启蒙"按钮
{
  upgradeType: "kindergarten",
  upgradeCategory: "拼音启蒙",
  category: "幼升小"
}
```

### 小升初按钮  
```javascript
// 用户点击"语文冲刺"按钮
{
  upgradeType: "primary", 
  upgradeCategory: "语文冲刺",
  category: "小升初"
}
```

## 注意事项

- upgradeCategory 字段直接使用中文分类名称
- 不需要英文映射，代码已简化
- 分类固定不可修改，确保数据一致性
- 后台界面直接显示中文分类名，用户选择后自动填充对应字段
