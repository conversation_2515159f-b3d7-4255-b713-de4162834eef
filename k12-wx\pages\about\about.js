// pages/about/about.js
Page({
  data: {
    appInfo: {
      name: 'K12教育资源',
      version: '1.0.0',
      description: '专为K12教育打造的优质教学资源分享平台',
      features: [
        '海量优质教学资源',
        '按年级学科精准分类',
        '智能搜索快速定位',
        '积分系统激励分享',
        '个人收藏便捷管理'
      ]
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '************',
      workTime: '工作日 9:00-18:00'
    },
    teamInfo: [
      {
        role: '产品经理',
        name: '张老师',
        description: '10年教育行业经验，专注K12教育产品设计'
      },
      {
        role: '技术负责人',
        name: '李工程师',
        description: '资深全栈开发工程师，教育技术专家'
      },
      {
        role: '教研顾问',
        name: '王教授',
        description: '知名教育专家，课程设计与教学方法研究'
      }
    ],
    updateLogs: [
      {
        version: '1.0.0',
        date: '2024-01-15',
        content: [
          '正式版本发布',
          '完整的资源分类体系',
          '积分系统上线',
          '用户个人中心功能'
        ]
      }
    ]
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '关于我们'
    });
  },

  // 复制联系方式
  copyContact(e) {
    const { type, value } = e.currentTarget.dataset;
    wx.setClipboardData({
      data: value,
      success: () => {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success'
        });
      }
    });
  },

  // 拨打电话
  makeCall() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  // 意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    });
    
    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      });
    }, 1500);
  },

  // 分享应用
  onShareAppMessage() {
    return {
      title: 'K12教育资源 - 优质教学资源分享平台',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});