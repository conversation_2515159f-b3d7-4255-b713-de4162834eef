<view class="container">
  <!-- 顶部导航栏 -->
  <view class="header">
    <view class="nav-bar">
      <view class="back-btn" bindtap="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="page-title">{{pageTitle}}</view>
      <view class="search-btn" bindtap="goToSearch">
        <text class="search-icon">🔍</text>
      </view>
    </view>
  </view>

  <!-- 筛选器区域 -->
  <view class="filter-section">
    <view class="filter-row">
      <view class="filter-item" bindtap="showSubjectFilter">
        <text class="filter-text">{{selectedSubject || '科目'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showVolumeFilter">
        <text class="filter-text">{{selectedVolume || '册别'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showTypeFilter">
        <text class="filter-text">{{selectedType || '板块'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      <view class="filter-item" bindtap="showSortFilter">
        <text class="filter-text">{{selectedSort || '排序'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>
    
    <!-- 已选筛选条件 -->
    <view class="selected-filters" wx:if="{{hasSelectedFilters}}">
      <view class="filter-tag" wx:for="{{selectedFilters}}" wx:key="type" bindtap="removeFilter" data-type="{{item.type}}">
        <text class="tag-text">{{item.text}}</text>
        <text class="tag-close">×</text>
      </view>
      <view class="clear-all" bindtap="clearAllFilters">
        <text>清空</text>
      </view>
    </view>
  </view>

  <!-- 资料列表 -->
  <view class="material-list">
    <view class="list-header">
      <text class="result-count">共{{totalCount}}个资料</text>
    </view>
    
    <view class="material-item" wx:for="{{materialList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <view class="material-cover">
        <image wx:if="{{item.cover}}" src="{{item.cover}}" mode="aspectFill" />
        <view wx:else class="cover-placeholder">
          <text class="cover-icon">📚</text>
        </view>
      </view>
      
      <view class="material-info">
        <view class="material-title">{{item.title}}</view>
        <view class="material-tags">
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        <view class="material-meta">
          <view class="meta-stats">
            <text class="stat-item">⬇ {{item.downloadCount}}</text>
            <text class="stat-item">👁 {{item.viewCount}}</text>
          </view>
          <view class="material-price">
            <text class="price-text">{{item.points}}积分</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-section" wx:if="{{!loading && materialList.length === 0}}">
    <view class="empty-icon">📚</view>
    <view class="empty-title">暂无资料</view>
    <view class="empty-subtitle">试试调整筛选条件</view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="filter-modal" wx:if="{{showFilterModal}}" bindtap="hideFilterModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{currentFilterTitle}}</text>
        <view class="modal-close" bindtap="hideFilterModal">×</view>
      </view>
      <view class="modal-body">
        <view class="filter-option" 
              wx:for="{{currentFilterOptions}}" 
              wx:key="value" 
              bindtap="selectFilterOption" 
              data-value="{{item.value}}"
              data-text="{{item.text}}">
          <text class="option-text {{item.selected ? 'selected' : ''}}">{{item.text}}</text>
          <view class="option-check" wx:if="{{item.selected}}">✓</view>
        </view>
      </view>
    </view>
  </view>
</view>