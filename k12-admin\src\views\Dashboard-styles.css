/* 数据概览页面样式补充 - 支持新增的注释元素 */

/* 统计卡片描述文字样式 */
.stats-description {
  font-size: 10px;
  color: #C0C4CC;
  margin-top: 2px;
  line-height: 1.2;
  display: block;
}

/* 图表卡片副标题样式 */
.chart-subtitle {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
  font-weight: normal;
}

/* 卡片头部布局优化 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 4px;
}

.card-header small {
  flex-basis: 100%;
  margin-top: 4px;
  order: 2;
}

.card-header .el-button {
  order: 3;
  margin-left: auto;
}

/* 响应式优化 - 移动端隐藏部分描述文字 */
@media (max-width: 768px) {
  .stats-description {
    display: none;
  }
  
  .chart-subtitle {
    font-size: 11px;
  }
}

/* 统计卡片高度调整以适应新增描述 */
@media (min-width: 769px) {
  .stats-card {
    height: 110px;
  }
}