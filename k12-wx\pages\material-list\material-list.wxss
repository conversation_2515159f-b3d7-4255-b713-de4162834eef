.container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 顶部导航 */
.header {
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  padding-top: 88rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 40rpx;
  color: white;
}

.back-btn, .search-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
}

.back-icon, .search-icon {
  font-size: 36rpx;
  font-weight: 600;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

/* 筛选器区域 */
.filter-section {
  background: white;
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.filter-row {
  display: flex;
  gap: 24rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 40rpx;
  border: 2rpx solid #e9ecef;
  min-height: 80rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}

.filter-arrow {
  font-size: 24rpx;
  color: #666;
}

/* 已选筛选条件 */
.selected-filters {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.filter-tag {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  border: 2rpx solid rgba(22, 119, 255, 0.2);
  border-radius: 32rpx;
}

.tag-text {
  font-size: 24rpx;
  color: #1677FF;
  margin-right: 12rpx;
}

.tag-close {
  font-size: 28rpx;
  color: #1677FF;
  font-weight: 600;
}

.clear-all {
  padding: 12rpx 24rpx;
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 32rpx;
  color: #ff4d4f;
  font-size: 24rpx;
}

/* 资料列表 */
.material-list {
  padding: 0 40rpx;
}

.list-header {
  padding: 32rpx 0 24rpx;
}

.result-count {
  font-size: 28rpx;
  color: #666;
}

.material-item {
  display: flex;
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
  border: 2rpx solid #f0f0f0;
}

.material-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 24rpx;
  overflow: hidden;
  margin-right: 32rpx;
  flex-shrink: 0;
}

.material-cover image {
  width: 100%;
  height: 100%;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #F0F7FF 0%, #E6F4FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(22, 119, 255, 0.1);
}

.cover-icon {
  font-size: 64rpx;
  color: #1677FF;
}

.material-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.material-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #E6F4FF 0%, #F0F7FF 100%);
  color: #1677FF;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 20rpx;
  border: 2rpx solid rgba(22, 119, 255, 0.15);
}

.material-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #999;
}

.material-price {
  background: linear-gradient(135deg, #FFE7E0 0%, #FFF2EF 100%);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
}

.price-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF6B35;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #f0f0f0;
  border-top: 6rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 160rpx 40rpx;
}

.empty-icon {
  font-size: 128rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 筛选弹窗 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  width: 100%;
  max-height: 60vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  border-radius: 32rpx;
  background: #f8f9fa;
}

.modal-body {
  max-height: calc(60vh - 160rpx);
  overflow-y: auto;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.filter-option:last-child {
  border-bottom: none;
}

.option-text {
  font-size: 32rpx;
  color: #333;
}

.option-text.selected {
  color: #1677FF;
  font-weight: 600;
}

.option-check {
  font-size: 36rpx;
  color: #1677FF;
  font-weight: 600;
}
