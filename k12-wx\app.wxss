/* 全局样式 - 基于TDesign设计体系 */

/* CSS变量定义 */
page {
  /* 主题色彩 */
  --brand-color: #1677FF;
  --brand-color-light: #E6F4FF;
  --function-color: #FF8800;
  --success-color: #00A870;
  --warning-color: #ED7B2F;
  --error-color: #D54941;
  
  /* 中性色彩 */
  --text-primary: #000000;
  --text-secondary: #4B4B4B;
  --text-placeholder: #8B8B8B;
  --text-disabled: #BBBBBB;
  
  /* 背景色彩 */
  --bg-color-page: #F8F9FA;
  --bg-color-container: #FFFFFF;
  --bg-color-tag: #F2F3F5;
  --border-color: #E6E6E6;
  --mask-color: rgba(0, 0, 0, 0.6);
  
  /* 字体大小 - 使用偶数rpx单位确保清晰显示 */
  --font-size-xl: 36rpx;
  --font-size-l: 32rpx;
  --font-size-m: 28rpx;
  --font-size-s: 24rpx;
  --font-size-xs: 20rpx;
  
  /* 额外的字体大小变量 */
  --font-size-xxl: 40rpx;
  --font-size-title: 34rpx;
  --font-size-subtitle: 26rpx;
  --font-size-caption: 22rpx;
  
  /* 间距 - 使用rpx单位 */
  --spacing-xs: 8rpx;
  --spacing-s: 16rpx;
  --spacing-m: 24rpx;
  --spacing-l: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 圆角 - 使用rpx单位 */
  --radius-s: 8rpx;
  --radius-m: 12rpx;
  --radius-l: 16rpx;
  
  /* 阴影 */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-button: 0 2px 4px rgba(0, 82, 217, 0.2);
  --shadow-popup: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 页面基础样式 */
page {
  background-color: #F8F9FA;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  color: #000000;
  line-height: 1.5;
  /* 字体平滑优化 - 解决字体模糊问题 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  /* 确保字体渲染清晰 */
  font-variant-ligatures: none;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

/* 全局字体优化 - 应用到常用元素 */
view, text, button, input, textarea, label, navigator {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 通用容器样式 */
.container {
  padding: 0 32rpx;
  background-color: #F8F9FA;
}

.card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-content {
  padding: 24rpx;
}

/* 文字样式 */
.text-xl {
  font-size: 36rpx;
  font-weight: 500;
  color: #000000;
}

.text-l {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.text-m {
  font-size: 28rpx;
  color: #4B4B4B;
}

.text-s {
  font-size: 24rpx;
  color: #8B8B8B;
}

.text-primary {
  color: #000000;
}

.text-secondary {
  color: #4B4B4B;
}

.text-placeholder {
  color: #8B8B8B;
}

.text-brand {
  color: #1677FF;
}

.text-function {
  color: #FF8800;
  font-weight: 600;
}

.text-success {
  color: #00A870;
}

.text-warning {
  color: #ED7B2F;
}

.text-error {
  color: #D54941;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f8f9fa;
  color: #495057;
  border: 2rpx solid #dee2e6;
}

.btn-secondary:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.btn-small {
  height: 64rpx;
  padding: 0 16rpx;
  font-size: 24rpx;
}

.btn-large {
  height: 104rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
}

.btn-disabled {
  background-color: #F2F3F5;
  color: #BBBBBB;
  box-shadow: none;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 4rpx 16rpx;
  background-color: #F2F3F5;
  color: #4B4B4B;
  font-size: 20rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

.tag-primary {
  background-color: #E6F4FF;
  color: #1677FF;
}

.tag-function {
  background-color: #FFF2E6;
  color: #FF8800;
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #FFFFFF;
  border-bottom: 2rpx solid #E6E6E6;
  transition: background-color 0.2s ease;
}

.list-item:active {
  background-color: #F8F9FA;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  margin-left: 24rpx;
}

.list-item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #8B8B8B;
}

.list-item-extra {
  color: #8B8B8B;
  font-size: 24rpx;
}

/* 图片样式 */
.image-cover {
  width: 128rpx;
  height: 128rpx;
  border-radius: 8rpx;
  background-color: #F2F3F5;
}

.image-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #F2F3F5;
}

/* 输入框样式 */
.input {
  height: 88rpx;
  padding: 0 24rpx;
  background-color: #FFFFFF;
  border: 2rpx solid #E6E6E6;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #000000;
}

.input:focus {
  border-color: #1677FF;
}

.input-placeholder {
  color: #8B8B8B;
}

/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #F2F3F5;
  border-radius: 36rpx;
  margin: 24rpx 32rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #4B4B4B;
  background: transparent;
  border: none;
  outline: none;
}

/* 网格布局 */
.grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(32rpx - 16rpx);
}

.grid-item {
  flex: 0 0 calc(50% - 16rpx * 2);
  margin: 16rpx;
}

.grid-item-3 {
  flex: 0 0 calc(33.333% - 16rpx * 2);
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #E6E6E6;
  margin: 24rpx 0;
}

.divider-thick {
  height: 16rpx;
  background-color: #F8F9FA;
  margin: 0;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  color: #8B8B8B;
  font-size: 24rpx;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  color: #8B8B8B;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #8B8B8B;
}

/* 固定底部操作栏 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  border-top: 2rpx solid #E6E6E6;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.action-bar {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 88rpx;
  height: 88rpx;
  border-radius: 12rpx;
  background-color: transparent;
  border: 2rpx solid #E6E6E6;
  color: #4B4B4B;
}

.action-btn:active {
  background-color: #F2F3F5;
}

.action-btn-primary {
  flex: 1;
  width: auto;
  background-color: #1677FF;
  color: #FFFFFF;
  border: none;
  font-weight: 500;
}

.action-btn-function {
  flex: 1;
  width: auto;
  background-color: #FF8800;
  color: #FFFFFF;
  border: none;
  font-weight: 500;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  margin: 32rpx;
  max-width: 640rpx;
  width: 100%;
}

.popup-header {
  padding: 32rpx 32rpx 24rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.popup-content {
  padding: 0 32rpx 32rpx;
}

.popup-footer {
  display: flex;
  border-top: 2rpx solid #E6E6E6;
}

.popup-btn {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #4B4B4B;
  border: none;
  background: transparent;
}

.popup-btn:first-child {
  border-right: 2rpx solid #E6E6E6;
}

.popup-btn-primary {
  color: #1677FF;
  font-weight: 500;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.bold {
  font-weight: 500;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}