<template>
  <div class="system-config">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>系统配置</h2>
      <p class="page-description">管理系统基础设置和参数配置</p>
    </div>

    <!-- 配置选项卡 -->
    <el-tabs v-model="activeTab" class="config-tabs">
      <!-- 基础设置 -->
      <el-tab-pane label="基础设置" name="basic">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>小程序基础信息</span>
              <el-button type="primary" @click="saveBasicConfig" :loading="saving">
                保存设置
              </el-button>
            </div>
          </template>
          
          <el-form :model="basicConfig" :rules="basicRules" ref="basicFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="小程序名称" prop="miniProgramName">
                  <el-input v-model="basicConfig.miniProgramName" placeholder="请输入小程序名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="小程序版本" prop="miniProgramVersion">
                  <el-input v-model="basicConfig.miniProgramVersion" placeholder="请输入小程序版本" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="管理员邮箱" prop="adminEmail">
                  <el-input v-model="basicConfig.adminEmail" placeholder="请输入管理员邮箱" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服电话" prop="supportPhone">
                  <el-input v-model="basicConfig.supportPhone" placeholder="请输入客服电话" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="小程序介绍" prop="miniProgramDescription">
              <el-input 
                v-model="basicConfig.miniProgramDescription" 
                type="textarea" 
                :rows="3"
                placeholder="请输入小程序介绍，将显示在小程序首页"
              />
            </el-form-item>
            
            <el-form-item label="首页推荐内容" prop="recommendedContent">
              <el-input 
                v-model="basicConfig.recommendedContent" 
                type="textarea" 
                :rows="2"
                placeholder="请输入首页推荐内容，支持多行文本"
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="小程序状态">
                  <el-switch 
                    v-model="basicConfig.miniProgramEnabled" 
                    active-text="启用" 
                    inactive-text="停用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="维护模式">
                  <el-switch 
                    v-model="basicConfig.maintenanceMode" 
                    active-text="开启" 
                    inactive-text="关闭"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="维护提示信息" prop="maintenanceMessage" v-if="basicConfig.maintenanceMode">
              <el-input 
                v-model="basicConfig.maintenanceMessage" 
                type="textarea" 
                :rows="2"
                placeholder="请输入维护期间显示给用户的提示信息"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 功能开关 -->
      <el-tab-pane label="功能开关" name="features">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>小程序功能开关</span>
              <el-button type="primary" @click="saveFeaturesConfig" :loading="saving">
                保存设置
              </el-button>
            </div>
          </template>
          
          <el-form :model="featuresConfig" ref="featuresFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="观看广告功能">
                  <el-switch 
                    v-model="featuresConfig.watchAdEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">用户观看广告获得积分</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分享功能">
                  <el-switch 
                    v-model="featuresConfig.shareEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">用户分享文件获得积分</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="收藏功能">
                  <el-switch 
                    v-model="featuresConfig.favoriteEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">用户收藏喜欢的文件</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="新用户注册">
                  <el-switch 
                    v-model="featuresConfig.registerEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">是否允许新用户注册</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="评论功能">
                  <el-switch 
                    v-model="featuresConfig.commentEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">用户对文件进行评论</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="搜索功能">
                  <el-switch 
                    v-model="featuresConfig.searchEnabled" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                  <div class="feature-desc">用户搜索文件功能</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 业务参数 -->
      <el-tab-pane label="业务参数" name="business">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>业务参数配置</span>
              <el-button type="primary" @click="saveBusinessConfig" :loading="saving">
                保存设置
              </el-button>
            </div>
          </template>
          
          <el-form :model="businessConfig" :rules="businessRules" ref="businessFormRef" label-width="140px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="文件预览页数" prop="previewPages">
                  <el-input-number 
                    v-model="businessConfig.previewPages" 
                    :min="1" 
                    :max="10"
                    placeholder="免费预览页数"
                  />
                  <div class="param-desc">用户免费预览的页数</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="默认积分价格" prop="defaultPointPrice">
                  <el-input-number 
                    v-model="businessConfig.defaultPointPrice" 
                    :min="1" 
                    :max="100"
                    placeholder="默认积分价格"
                  />
                  <div class="param-desc">新上传文件的默认积分价格</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="文件大小限制" prop="maxFileSize">
                  <el-input-number 
                    v-model="businessConfig.maxFileSize" 
                    :min="1" 
                    :max="500"
                    placeholder="文件大小限制(MB)"
                  />
                  <div class="param-desc">单个文件最大上传大小</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="每日积分上限" prop="dailyPointLimit">
                  <el-input-number 
                    v-model="businessConfig.dailyPointLimit" 
                    :min="10" 
                    :max="1000"
                    placeholder="每日积分获取上限"
                  />
                  <div class="param-desc">用户每日最多获得的积分数</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="新用户注册积分" prop="registerBonus">
                  <el-input-number 
                    v-model="businessConfig.registerBonus" 
                    :min="0" 
                    :max="100"
                    placeholder="新用户注册奖励积分"
                  />
                  <div class="param-desc">新用户注册获得的积分奖励</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分享奖励积分" prop="shareBonus">
                  <el-input-number 
                    v-model="businessConfig.shareBonus" 
                    :min="0" 
                    :max="50"
                    placeholder="分享奖励积分"
                  />
                  <div class="param-desc">用户分享文件获得的积分</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="观看广告积分" prop="adBonus">
                  <el-input-number 
                    v-model="businessConfig.adBonus" 
                    :min="0" 
                    :max="20"
                    placeholder="观看广告积分"
                  />
                  <div class="param-desc">用户观看广告获得的积分</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="每日签到积分" prop="dailySignBonus">
                  <el-input-number 
                    v-model="businessConfig.dailySignBonus" 
                    :min="0" 
                    :max="10"
                    placeholder="每日签到积分"
                  />
                  <div class="param-desc">用户每日签到获得的积分</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 存储设置 -->
      <el-tab-pane label="存储设置" name="storage">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>文件存储配置</span>
              <el-button type="primary" @click="saveStorageConfig" :loading="saving">
                保存设置
              </el-button>
            </div>
          </template>
          
          <el-form :model="storageConfig" :rules="storageRules" ref="storageFormRef" label-width="120px">
            <el-form-item label="存储类型" prop="storageType">
              <el-radio-group v-model="storageConfig.storageType">
                <el-radio label="cloud">
                  <div class="storage-option">
                    <div class="option-title">云存储</div>
                    <div class="option-desc">使用腾讯云开发云存储，支持CDN加速，推荐使用</div>
                  </div>
                </el-radio>
                <el-radio label="local">
                  <div class="storage-option">
                    <div class="option-title">本地存储</div>
                    <div class="option-desc">文件存储在本地服务器，适用于开发测试环境</div>
                  </div>
                </el-radio>
                <el-radio label="third-party">
                  <div class="storage-option">
                    <div class="option-title">第三方存储</div>
                    <div class="option-desc">对接阿里云OSS、腾讯云COS等第三方存储服务</div>
                  </div>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="存储说明">
              <el-alert 
                v-if="storageConfig.storageType === 'cloud'"
                title="当前使用云开发环境的云存储，文件将自动上传到云端并获得CDN加速"
                type="info"
                :closable="false"
              />
              <el-alert 
                v-if="storageConfig.storageType === 'local'"
                title="本地存储模式仅适用于开发环境，生产环境建议使用云存储"
                type="warning"
                :closable="false"
              />
              <el-alert 
                v-if="storageConfig.storageType === 'third-party'"
                title="使用第三方存储需要配置相应的API密钥和存储桶信息"
                type="warning"
                :closable="false"
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大存储空间" prop="maxStorageSize">
                  <el-input v-model="storageConfig.maxStorageSize" placeholder="请输入最大存储空间(GB)">
                    <template #append>GB</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单文件大小限制" prop="maxFileSize">
                  <el-input v-model="storageConfig.maxFileSize" placeholder="请输入单文件大小限制(MB)">
                    <template #append>MB</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="允许的文件类型" prop="allowedFileTypes">
              <el-select 
                v-model="storageConfig.allowedFileTypes" 
                multiple 
                placeholder="请选择允许的文件类型"
              >
                <el-option label="PDF文档" value="pdf" />
                <el-option label="Word文档" value="doc,docx" />
                <el-option label="Excel表格" value="xls,xlsx" />
                <el-option label="PowerPoint" value="ppt,pptx" />
                <el-option label="图片文件" value="jpg,jpeg,png,gif" />
                <el-option label="视频文件" value="mp4,avi,mov" />
                <el-option label="音频文件" value="mp3,wav,aac" />
              </el-select>
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="自动清理">
                  <el-switch 
                    v-model="storageConfig.autoCleanup" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="清理周期" prop="cleanupDays" v-if="storageConfig.autoCleanup">
                  <el-input v-model="storageConfig.cleanupDays" placeholder="请输入清理周期(天)">
                    <template #append>天</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 开发环境设置 -->
      <el-tab-pane label="开发环境设置" name="security">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>开发环境配置</span>
              <el-button type="primary" @click="saveSecurityConfig" :loading="saving">
                保存设置
              </el-button>
            </div>
          </template>
          
          <el-alert 
            title="当前为本地开发环境，以下配置主要用于开发调试"
            type="info"
            :closable="false"
            style="margin-bottom: 20px;"
          />
          
          <el-form :model="securityConfig" :rules="securityRules" ref="securityFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="调试模式">
                  <el-switch 
                    v-model="securityConfig.debugMode" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="控制台日志">
                  <el-switch 
                    v-model="securityConfig.consoleLog" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="API请求日志">
                  <el-switch 
                    v-model="securityConfig.apiLog" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="错误详情显示">
                  <el-switch 
                    v-model="securityConfig.showErrorDetails" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="开发环境说明">
              <el-input 
                v-model="securityConfig.devNotes" 
                type="textarea" 
                :rows="3"
                placeholder="可以在这里记录开发环境的相关说明和注意事项"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  getSystemConfigs,
  updateSystemConfig
} from '../api/systemConfig'

// 当前激活的选项卡
const activeTab = ref('basic')
const saving = ref(false)

// 表单引用
const basicFormRef = ref<FormInstance>()
const featuresFormRef = ref<FormInstance>()
const businessFormRef = ref<FormInstance>()
const storageFormRef = ref<FormInstance>()
const securityFormRef = ref<FormInstance>()

// 基础设置配置
const basicConfig = reactive({
  miniProgramName: '',
  miniProgramVersion: '',
  adminEmail: '',
  supportPhone: '',
  miniProgramDescription: '',
  recommendedContent: '',
  miniProgramEnabled: true,
  maintenanceMode: false,
  maintenanceMessage: ''
})

// 功能开关配置
const featuresConfig = reactive({
  watchAdEnabled: true,
  shareEnabled: true,
  favoriteEnabled: true,
  registerEnabled: true,
  commentEnabled: true,
  searchEnabled: true
})

// 业务参数配置
const businessConfig = reactive({
  previewPages: 3,
  defaultPointPrice: 5,
  maxFileSize: 50,
  dailyPointLimit: 100,
  registerBonus: 20,
  shareBonus: 5,
  adBonus: 2,
  dailySignBonus: 1
})

// 存储设置配置
const storageConfig = reactive({
  storageType: 'cloud',
  maxStorageSize: '100',
  maxFileSize: '50',
  allowedFileTypes: ['pdf', 'doc,docx', 'xls,xlsx', 'jpg,jpeg,png,gif'],
  autoCleanup: true,
  cleanupDays: '30'
})

// 开发环境设置配置
const securityConfig = reactive({
  debugMode: true,
  consoleLog: true,
  apiLog: true,
  showErrorDetails: true,
  devNotes: ''
})

// 表单验证规则
const basicRules: FormRules = {
  miniProgramName: [
    { required: true, message: '请输入小程序名称', trigger: 'blur' }
  ],
  miniProgramVersion: [
    { required: true, message: '请输入小程序版本', trigger: 'blur' }
  ],
  adminEmail: [
    { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const businessRules: FormRules = {
  previewPages: [
    { required: true, message: '请设置预览页数', trigger: 'blur' }
  ],
  defaultPointPrice: [
    { required: true, message: '请设置默认积分价格', trigger: 'blur' }
  ],
  maxFileSize: [
    { required: true, message: '请设置文件大小限制', trigger: 'blur' }
  ],
  dailyPointLimit: [
    { required: true, message: '请设置每日积分上限', trigger: 'blur' }
  ]
}

const storageRules: FormRules = {
  storageType: [
    { required: true, message: '请选择存储类型', trigger: 'change' }
  ],
  maxStorageSize: [
    { required: true, message: '请输入最大存储空间', trigger: 'blur' }
  ],
  maxFileSize: [
    { required: true, message: '请输入单文件大小限制', trigger: 'blur' }
  ]
}

const securityRules: FormRules = {
  // 开发环境设置不需要复杂的验证规则
}

// 保存配置函数
const saveBasicConfig = async () => {
  if (!basicFormRef.value) return
  
  try {
    await basicFormRef.value.validate()
    saving.value = true
    
    await updateSystemConfig('basic', basicConfig)
    ElMessage.success('基础设置保存成功')
  } catch (error) {
    console.error('保存基础设置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const saveFeaturesConfig = async () => {
  saving.value = true
  
  try {
    await updateSystemConfig('features', featuresConfig)
    ElMessage.success('功能开关保存成功')
  } catch (error) {
    console.error('保存功能开关失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const saveBusinessConfig = async () => {
  if (!businessFormRef.value) return
  
  try {
    await businessFormRef.value.validate()
    saving.value = true
    
    await updateSystemConfig('business', businessConfig)
    ElMessage.success('业务参数保存成功')
  } catch (error) {
    console.error('保存业务参数失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const saveStorageConfig = async () => {
  if (!storageFormRef.value) return
  
  try {
    await storageFormRef.value.validate()
    saving.value = true
    
    await updateSystemConfig('storage', storageConfig)
    ElMessage.success('存储设置保存成功')
  } catch (error) {
    console.error('保存存储设置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const saveSecurityConfig = async () => {
  if (!securityFormRef.value) return
  
  try {
    await securityFormRef.value.validate()
    saving.value = true
    
    await updateSystemConfig('security', securityConfig)
    ElMessage.success('开发环境设置保存成功')
  } catch (error) {
    console.error('保存开发环境设置失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 加载配置数据
const loadConfigs = async () => {
  try {
    const configs = await getSystemConfigs()
    
    // 更新各个配置对象
    if (configs.basic) {
      Object.assign(basicConfig, configs.basic)
    }
    if (configs.features) {
      Object.assign(featuresConfig, configs.features)
    }
    if (configs.business) {
      Object.assign(businessConfig, configs.business)
    }
    if (configs.storage) {
      Object.assign(storageConfig, configs.storage)
    }
    if (configs.security) {
      Object.assign(securityConfig, configs.security)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置数据失败')
  }
}

onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.config-tabs {
  margin-top: 20px;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: 600;
  color: #303133;
}

.storage-option {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.option-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.option-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.feature-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.param-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-config {
    padding: 16px;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
