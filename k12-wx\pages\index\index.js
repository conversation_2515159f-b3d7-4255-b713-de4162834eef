// 首页逻辑
const app = getApp()

Page({
  data: {
    // 年级列表 - 使用更专业的图标标识
    gradeList: [
      { name: '一年级', icon: 'icon-grade1', iconText: '启蒙' },
      { name: '二年级', icon: 'icon-grade2', iconText: '基础' },
      { name: '三年级', icon: 'icon-grade3', iconText: '成长' },
      { name: '四年级', icon: 'icon-grade4', iconText: '进阶' },
      { name: '五年级', icon: 'icon-grade5', iconText: '提升' },
      { name: '六年级', icon: 'icon-grade6', iconText: '冲刺' }
    ],

    // 升学专区列表
    upgradeList: [
      { 
        title: '幼升小', 
        subtitle: '入学准备', 
        type: 'kindergarten', 
        emoji: '🎒'
      },
      { 
        title: '小升初', 
        subtitle: '升学冲刺', 
        type: 'primary', 
        emoji: '🎓'
      }
    ],
    
    // 热门资料列表
    hotMaterials: [],
    
    // 加载状态
    loading: false
  },

  onLoad() {
    // 延迟加载，确保云环境已初始化
    setTimeout(() => {
      this.loadHotMaterials()
    }, 200)
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadHotMaterials()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadHotMaterials().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载热门资料
  async loadHotMaterials() {
    this.setData({ loading: true })
    
    try {
      console.log('开始调用云函数获取热门资料...')
      
      // 直接调用云函数
      const res = await wx.cloud.callFunction({
        name: 'material-getList',
        data: {
          sortBy: 'downloadCount',
          pageSize: 5
        }
      })
      
      console.log('云函数调用结果:', res)
      
      if (res.result && res.result.success) {
        const materials = res.result.data || []
        console.log('获取到的资料数据:', materials)
        
        this.setData({
          hotMaterials: materials
        })
        
        if (materials.length === 0) {
          wx.showToast({
            title: '暂无热门资料',
            icon: 'none',
            duration: 2000
          })
        }
      } else {
        throw new Error(res.result?.message || '获取数据失败')
      }
      
    } catch (error) {
      console.error('加载热门资料失败:', error)
      
      this.setData({
        hotMaterials: []
      })
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 跳转到搜索页面
  goToSearch() {
    console.log('搜索框被点击了')
    wx.switchTab({
      url: '/pages/search/search',
      success: function(res) {
        console.log('跳转成功', res)
      },
      fail: function(err) {
        console.error('跳转失败', err)
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 选择年级
  goToGrade(e) {
    const grade = e.currentTarget.dataset.grade
    wx.navigateTo({
      url: `/pages/material-list/material-list?grade=${grade}&title=${grade}资料`
    })
  },

  // 跳转到升学专区
  goToUpgrade(e) {
    const { type, index } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/upgrade/upgrade?type=${type}`
    })
  },

  // 跳转到资料详情
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 跳转到更多热门资料页面
  goToMoreMaterials() {
    wx.navigateTo({
      url: '/pages/material-list/material-list?sortBy=downloadCount&title=热门资料'
    })
  }
})