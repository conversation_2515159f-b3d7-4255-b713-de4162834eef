const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { openid } = event
  
  try {
    // 使用传入的openid或者从上下文获取
    const userId = openid || wxContext.OPENID
    
    if (!userId) {
      return {
        success: false,
        error: '用户身份验证失败'
      }
    }

    // 查询用户的积分历史记录
    const result = await db.collection('points_history')
      .where({
        openid: userId
      })
      .orderBy('createTime', 'desc')
      .limit(50)
      .get()

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取积分历史失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}