<!--pages/my-favorites/my-favorites.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的收藏</text>
    <text class="page-subtitle">管理您收藏的学习资料</text>
  </view>

  <!-- 筛选和排序工具栏 -->
  <view class="toolbar">
    <view class="filter-section">
      <view class="filter-item" bindtap="onGradeFilter">
        <text class="filter-label">年级</text>
        <text class="filter-value">{{filterGrade || '全部'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" bindtap="onSubjectFilter">
        <text class="filter-label">学科</text>
        <text class="filter-value">{{filterSubject || '全部'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" bindtap="clearFilter" wx:if="{{filterGrade || filterSubject}}">
        <text class="clear-filter">清空</text>
      </view>
    </view>
    
    <view class="sort-section">
      <picker mode="selector" range="['按时间', '按名称']" value="{{sortType === 'time' ? 0 : 1}}" bindchange="onSortChange">
        <view class="sort-picker">
          <text class="sort-label">{{sortType === 'time' ? '按时间' : '按名称'}}</text>
          <text class="sort-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 编辑模式工具栏 -->
  <view class="edit-toolbar" wx:if="{{!isEmpty && !loading}}">
    <view class="edit-left">
      <view class="edit-btn" bindtap="toggleEditMode">
        <text>{{editMode ? '取消' : '编辑'}}</text>
      </view>
      <view class="select-all-btn" bindtap="toggleSelectAll" wx:if="{{editMode}}">
        <text>{{selectedItems.length === favorites.length ? '取消全选' : '全选'}}</text>
      </view>
    </view>
    <view class="edit-right" wx:if="{{editMode}}">
      <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{isEmpty && !loading}}">
    <image class="empty-icon" src="/images/empty-favorite.png" mode="aspectFit"></image>
    <text class="empty-title">暂无收藏</text>
    <text class="empty-subtitle">收藏喜欢的学习资料，方便随时查看</text>
    <navigator url="/pages/index/index" class="empty-button">
      <text>去首页看看</text>
    </navigator>
  </view>

  <!-- 收藏列表 -->
  <view class="favorite-list" wx:if="{{!isEmpty && !loading}}">
    <view class="favorite-item {{editMode ? 'edit-mode' : ''}}" wx:for="{{favorites}}" wx:key="id">
      <!-- 选择框 -->
      <view class="select-checkbox" wx:if="{{editMode}}" bindtap="toggleSelectItem" data-id="{{item.id}}">
        <view class="checkbox {{selectedItems.indexOf(item.id) > -1 ? 'checked' : ''}}">
          <text class="checkbox-icon" wx:if="{{selectedItems.indexOf(item.id) > -1}}">✓</text>
        </view>
      </view>
      
      <view class="item-content" bindtap="viewMaterial" data-id="{{item.id}}">
        <view class="item-cover">
          <image src="{{item.cover || '/images/default-cover.png'}}" mode="aspectFill"></image>
          <view class="item-type">{{item.type}}</view>
        </view>
        
        <view class="item-info">
          <view class="item-title">{{item.title}}</view>
          <view class="item-meta">
            <text class="meta-item">{{item.grade}}</text>
            <text class="meta-separator">·</text>
            <text class="meta-item">{{item.subject}}</text>
            <text class="meta-separator">·</text>
            <text class="meta-item">{{item.size}}</text>
          </view>
          <view class="item-stats">
            <text class="stats-item">下载 {{item.downloadCount}}</text>
            <text class="stats-separator">·</text>
            <text class="stats-item">评分 {{item.rating}}</text>
          </view>
          <view class="item-time">收藏时间：{{item.favoriteTime}}</view>
        </view>
      </view>
      
      <!-- 单项操作按钮 -->
      <view class="item-actions" wx:if="{{!editMode}}">
        <view class="action-btn remove-btn" bindtap="removeFavorite" data-id="{{item.id}}">
          <text class="action-icon">♡</text>
          <text class="action-text">取消收藏</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-toolbar" wx:if="{{!isEmpty && !loading}}">
    <view class="toolbar-info" wx:if="{{!editMode}}">
      <text>共 {{favorites.length}} 个收藏</text>
    </view>
    <view class="toolbar-actions">
      <view class="toolbar-btn delete-btn" bindtap="deleteSelected" wx:if="{{editMode}}">
        <text>删除选中</text>
      </view>
      <view class="toolbar-btn clear-btn" bindtap="clearAll" wx:if="{{!editMode}}">
        <text>清空全部</text>
      </view>
    </view>
  </view>
</view>