const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, userId, openid, points = 0, reason = '', extra = {} } = event
  
  try {
    // 使用传入的userId、openid或者从上下文获取
    const userOpenid = userId || openid || wxContext.OPENID
    
    if (!userOpenid) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }

    const now = new Date()
    const today = now.toISOString().split('T')[0] // YYYY-MM-DD格式
    let pointsToAdd = 0
    let actionDescription = ''

    // 根据不同的action处理积分逻辑
    switch (action) {
      case 'signin':
      case 'signIn':
        // 检查今日是否已签到
        const todaySignIn = await db.collection('points_history')
          .where({
            openid: userOpenid,
            action: 'signin',
            date: today
          })
          .get()

        if (todaySignIn.data.length > 0) {
          return {
            success: false,
            message: '今日已签到'
          }
        }

        pointsToAdd = 10 // 签到奖励10积分
        actionDescription = '每日签到'
        break

      case 'share':
        // 检查今日分享次数（限制每日最多3次）
        const todayShares = await db.collection('points_history')
          .where({
            openid: userOpenid,
            action: 'share',
            date: today
          })
          .get()

        if (todayShares.data.length >= 3) {
          return {
            success: false,
            message: '今日分享次数已达上限'
          }
        }

        pointsToAdd = 5 // 分享奖励5积分
        actionDescription = '分享资料'
        break

      case 'invite':
        pointsToAdd = 20 // 邀请好友奖励20积分
        actionDescription = '邀请好友'
        break

      case 'newUser':
        pointsToAdd = 50 // 新用户奖励50积分
        actionDescription = '新用户奖励'
        break

      case 'watchAd':
        // 检查今日观看广告次数（限制每日最多5次）
        const todayAds = await db.collection('points_history')
          .where({
            openid: userOpenid,
            action: 'watchAd',
            date: today
          })
          .get()

        if (todayAds.data.length >= 5) {
          return {
            success: false,
            message: '今日观看广告次数已达上限'
          }
        }

        pointsToAdd = 2 // 观看广告奖励2积分
        actionDescription = '观看广告'
        break

      case 'custom':
        pointsToAdd = points || 0
        actionDescription = reason || '自定义奖励'
        break

      default:
        return {
          success: false,
          message: '未知的积分操作类型'
        }
    }

    // 开始事务处理
    const transaction = await db.startTransaction()

    try {
      // 1. 更新用户积分
      const userResult = await transaction.collection('users')
        .where({
          openid: userOpenid
        })
        .update({
          data: {
            points: _.inc(pointsToAdd),
            updateTime: now
          }
        })

      // 2. 添加积分历史记录
      await transaction.collection('points_history').add({
        data: {
          openid: userOpenid,
          action: action,
          points: pointsToAdd,
          description: actionDescription,
          date: today,
          createTime: now,
          extra: extra
        }
      })

      // 提交事务
      await transaction.commit()

      return {
        success: true,
        data: {
          points: pointsToAdd,
          description: actionDescription,
          action: action
        },
        message: `${actionDescription}成功，获得${pointsToAdd}积分`
      }

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback()
      throw transactionError
    }

  } catch (error) {
    console.error('积分操作失败:', error)
    return {
      success: false,
      message: error.message || '积分操作失败'
    }
  }
}