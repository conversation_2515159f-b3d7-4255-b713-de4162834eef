// cloudfunctions/search-suggestions/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  console.log('获取搜索建议请求:', event)
  
  try {
    const { keyword = '' } = event
    
    if (!keyword || keyword.trim().length < 1) {
      return {
        success: true,
        data: [],
        message: '关键词为空'
      }
    }
    
    const searchKeyword = keyword.trim()
    const suggestions = new Set()
    
    // 1. 从资料标题中搜索匹配的建议
    const titleResult = await db.collection('files')
      .where({
        status: '已上架',
        title: db.RegExp({
          regexp: searchKeyword,
          options: 'i'
        })
      })
      .field({
        title: true
      })
      .limit(20)
      .get()
    
    // 提取标题中的关键词
    titleResult.data.forEach(item => {
      const title = item.title
      // 简单的关键词提取逻辑
      const words = title.split(/[，。、\s]+/).filter(word => 
        word.length >= 2 && 
        word.includes(searchKeyword) && 
        word !== searchKeyword
      )
      words.forEach(word => {
        if (suggestions.size < 10) {
          suggestions.add(word)
        }
      })
    })
    
    // 2. 从搜索历史中获取相关建议
    try {
      const historyResult = await db.collection('search_history')
        .where({
          keyword: db.RegExp({
            regexp: searchKeyword,
            options: 'i'
          })
        })
        .field({
          keyword: true
        })
        .orderBy('search_time', 'desc')
        .limit(10)
        .get()
      
      historyResult.data.forEach(item => {
        if (suggestions.size < 10 && item.keyword !== searchKeyword) {
          suggestions.add(item.keyword)
        }
      })
    } catch (historyError) {
      console.warn('获取搜索历史建议失败:', historyError)
    }
    
    // 3. 基于分类信息生成建议
    const categoryKeywords = [
      `${searchKeyword}试卷`,
      `${searchKeyword}练习册`,
      `${searchKeyword}知识点`,
      `${searchKeyword}素材`,
      `${searchKeyword}课件`
    ]
    
    categoryKeywords.forEach(keyword => {
      if (suggestions.size < 10) {
        suggestions.add(keyword)
      }
    })
    
    const result = Array.from(suggestions).slice(0, 8)
    
    console.log(`搜索建议结果: ${searchKeyword} -> ${result.length}条建议`)
    
    return {
      success: true,
      data: result,
      keyword: searchKeyword
    }
    
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    
    return {
      success: false,
      message: '获取搜索建议失败',
      error: error.message,
      data: []
    }
  }
}