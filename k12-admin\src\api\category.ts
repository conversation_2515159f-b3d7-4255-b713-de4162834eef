import cloudbase from '@cloudbase/js-sdk'

const app = cloudbase.init({
  env: 'cloud1-8gm001v7fd56ff43'
})

const auth = app.auth()
const db = app.database()

// 全局认证状态标记
let isAuthInitialized = false

// 确保认证状态 - 修复版本，避免重复创建认证对象
const ensureAuth = async () => {
  try {
    console.log('🔥 ensureAuth: 开始认证检查，当前状态:', isAuthInitialized)
    
    // 如果已经初始化过认证，直接返回成功
    if (isAuthInitialized) {
      console.log('🔥 ensureAuth: 认证已初始化，直接返回')
      return true
    }
    
    const loginState = await auth.getLoginState()
    console.log('🔥 ensureAuth: 当前登录状态:', loginState)
    
    if (!loginState) {
      console.log('🔥 ensureAuth: 未登录，开始匿名登录')
      // 只在未登录时进行匿名登录
      const result = await auth.signInAnonymously()
      console.log('🔥 ensureAuth: 匿名登录结果:', result)
    }
    
    // 获取最新的登录状态
    const newLoginState = await auth.getLoginState()
    console.log('🔥 ensureAuth: 最新登录状态:', newLoginState)
    
    // 标记认证已初始化
    isAuthInitialized = true
    return true
  } catch (error) {
    console.error('🔥 ensureAuth: 认证失败:', error)
    return false
  }
}

// 分类类型定义
export interface Category {
  _id?: string
  name: string
  type: 'grade' | 'subject' | 'volume' | 'section' // 年级、科目、册别、板块
  order: number // 排序
  status: 'active' | 'inactive' // 状态
  createTime?: string
  updateTime?: string
}

// 获取分类列表
export const getCategoryList = async (type?: string) => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      return {
        success: false,
        data: [],
        message: '认证失败，无法获取分类列表'
      }
    }

    let res
    
    if (type) {
      res = await db.collection('categories')
        .where({
          type: type,
          is_active: true
        })
        .orderBy('sort_order', 'asc')
        .get()
    } else {
      res = await db.collection('categories')
        .where({
          is_active: true
        })
        .orderBy('sort_order', 'asc')
        .get()
    }
    
    // 转换字段名以匹配前端期望的格式
    const transformedData = res.data.map((item: any) => ({
      _id: item._id,
      name: item.name,
      type: item.type,
      order: item.sort_order,
      status: item.is_active ? 'active' : 'inactive',
      createTime: item.created_time ? new Date(item.created_time.$date || item.created_time).toISOString() : '',
      updateTime: item.updated_time ? new Date(item.updated_time.$date || item.updated_time).toISOString() : ''
    }))
    
    return {
      success: true,
      data: transformedData,
      message: '获取分类列表成功'
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    return {
      success: false,
      data: [],
      message: '获取分类列表失败'
    }
  }
}

// 创建分类
export const createCategory = async (categoryData: Omit<Category, '_id' | 'createTime' | 'updateTime'>) => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      return {
        success: false,
        data: null,
        message: '认证失败，无法创建分类'
      }
    }

    const now = new Date()
    
    // 转换为数据库期望的字段格式
    const dbData = {
      name: categoryData.name,
      type: categoryData.type,
      sort_order: categoryData.order,
      is_active: categoryData.status === 'active',
      created_time: now,
      updated_time: now
    }
    
    const res = await db.collection('categories').add(dbData)
    
    return {
      success: true,
      data: res,
      message: '创建分类成功'
    }
  } catch (error) {
    console.error('创建分类失败:', error)
    return {
      success: false,
      data: null,
      message: '创建分类失败'
    }
  }
}

// 更新分类
export const updateCategory = async (id: string, updateData: Partial<Category>) => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      return {
        success: false,
        data: null,
        message: '认证失败，无法更新分类'
      }
    }

    const now = new Date()
    
    // 转换为数据库期望的字段格式
    const dbUpdateData: any = {
      updated_time: now
    }
    
    if (updateData.name !== undefined) dbUpdateData.name = updateData.name
    if (updateData.order !== undefined) dbUpdateData.sort_order = updateData.order
    if (updateData.status !== undefined) dbUpdateData.is_active = updateData.status === 'active'
    
    const res = await db.collection('categories').doc(id).update(dbUpdateData)
    
    return {
      success: true,
      data: res,
      message: '更新分类成功'
    }
  } catch (error) {
    console.error('更新分类失败:', error)
    return {
      success: false,
      data: null,
      message: '更新分类失败'
    }
  }
}

// 删除分类
export const deleteCategory = async (id: string) => {
  console.log('🔥 deleteCategory 开始执行，ID:', id)
  
  try {
    // 确保认证状态
    console.log('🔥 开始认证检查...')
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      console.log('🔥 认证失败')
      return {
        success: false,
        data: null,
        message: '认证失败，无法删除分类'
      }
    }
    console.log('🔥 认证成功')

    // 先查询当前记录状态
    console.log('🔥 查询当前记录状态...')
    const currentDoc = await db.collection('categories').doc(id).get()
    console.log('🔥 当前记录:', currentDoc.data)

    // 软删除，只修改状态
    console.log('🔥 开始执行软删除操作...')
    const updateData = {
      is_active: false,
      updated_time: new Date()
    }
    console.log('🔥 更新数据:', updateData)
    
    const res = await db.collection('categories').doc(id).update(updateData)
    console.log('🔥 删除操作结果:', res)
    
    // 验证删除结果
    console.log('🔥 验证删除结果...')
    const verifyDoc = await db.collection('categories').doc(id).get()
    console.log('🔥 删除后记录状态:', verifyDoc.data)
    
    return {
      success: true,
      data: res,
      message: '删除分类成功'
    }
  } catch (error) {
    console.error('🔥 删除分类失败:', error)
    return {
      success: false,
      data: null,
      message: '删除分类失败: ' + (error instanceof Error ? error.message : String(error))
    }
  }
}

// 初始化默认分类数据
export const initDefaultCategories = async () => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      return {
        success: false,
        message: '认证失败，无法初始化分类数据'
      }
    }

    // 检查是否已经初始化过
    const existingRes = await db.collection('categories').limit(1).get()
    if (existingRes.data.length > 0) {
      return {
        success: true,
        message: '分类数据已存在，无需初始化'
      }
    }

    const defaultCategories: Omit<Category, '_id' | 'createTime' | 'updateTime'>[] = [
      // 年级分类
      { name: '一年级', type: 'grade', order: 1, status: 'active' },
      { name: '二年级', type: 'grade', order: 2, status: 'active' },
      { name: '三年级', type: 'grade', order: 3, status: 'active' },
      { name: '四年级', type: 'grade', order: 4, status: 'active' },
      { name: '五年级', type: 'grade', order: 5, status: 'active' },
      { name: '六年级', type: 'grade', order: 6, status: 'active' },
      
      // 科目分类
      { name: '语文', type: 'subject', order: 1, status: 'active' },
      { name: '数学', type: 'subject', order: 2, status: 'active' },
      { name: '英语', type: 'subject', order: 3, status: 'active' },
      { name: '科学', type: 'subject', order: 4, status: 'active' },
      { name: '道德与法治', type: 'subject', order: 5, status: 'active' },
      { name: '音乐', type: 'subject', order: 6, status: 'active' },
      { name: '美术', type: 'subject', order: 7, status: 'active' },
      { name: '体育', type: 'subject', order: 8, status: 'active' },
      
      // 册别分类
      { name: '上册', type: 'volume', order: 1, status: 'active' },
      { name: '下册', type: 'volume', order: 2, status: 'active' },
      { name: '全册', type: 'volume', order: 3, status: 'active' },
      
      // 板块分类
      { name: '单元同步', type: 'section', order: 1, status: 'active' },
      { name: '期中试卷', type: 'section', order: 2, status: 'active' },
      { name: '期末试卷', type: 'section', order: 3, status: 'active' },
      { name: '练习册', type: 'section', order: 4, status: 'active' },
      { name: '课件资料', type: 'section', order: 5, status: 'active' },
      { name: '教学视频', type: 'section', order: 6, status: 'active' }
    ]

    const now = new Date()
    const categoriesWithTime = defaultCategories.map(cat => ({
      name: cat.name,
      type: cat.type,
      sort_order: cat.order,
      is_active: cat.status === 'active',
      created_time: now,
      updated_time: now
    }))

    // 批量插入
    for (const category of categoriesWithTime) {
      await db.collection('categories').add(category)
    }

    return {
      success: true,
      message: '默认分类数据初始化成功'
    }
  } catch (error) {
    console.error('初始化默认分类失败:', error)
    return {
      success: false,
      message: '初始化默认分类失败'
    }
  }
}
