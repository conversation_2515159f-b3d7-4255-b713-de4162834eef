/* pages/points-detail/points-detail.wxss */
.container {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 40rpx;
}

/* 积分概览卡片 */
.points-overview {
  margin: 32rpx;
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(22, 119, 255, 0.3);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
}

.action-btn {
  padding: 12rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.action-text {
  font-size: 28rpx;
  color: white;
}

.overview-content {
  text-align: center;
  margin-bottom: 32rpx;
}

.current-points {
  margin-bottom: 24rpx;
}

.points-number {
  display: block;
  font-size: 72rpx;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8rpx;
}

.points-label {
  font-size: 28rpx;
  opacity: 0.8;
}

.points-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 48rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.overview-actions {
  display: flex;
  gap: 16rpx;
}

.overview-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s;
}

.overview-btn:active {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  margin: 0 32rpx 24rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 8rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666666;
  transition: all 0.2s;
}

.tab-item.active {
  background-color: #1677FF;
  color: white;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #E6E6E6;
  border-top: 4rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666666;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.empty-button {
  padding: 24rpx 48rpx;
  background-color: #1677FF;
  color: white;
  border-radius: 48rpx;
  font-size: 28rpx;
}

/* 历史记录列表 */
.history-list {
  padding: 0 32rpx;
}

.history-item {
  display: flex;
  align-items: flex-start;
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.item-icon.earn {
  background-color: rgba(82, 196, 26, 0.1);
}

.item-icon.spend {
  background-color: rgba(255, 77, 79, 0.1);
}

.icon-text {
  font-size: 32rpx;
  font-weight: 600;
}

.item-icon.earn .icon-text {
  color: #52C41A;
}

.item-icon.spend .icon-text {
  color: #FF4D4F;
}

.item-content {
  flex: 1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.item-points {
  font-size: 32rpx;
  font-weight: 600;
}

.item-points.earn {
  color: #52C41A;
}

.item-points.spend {
  color: #FF4D4F;
}

.item-description {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.item-time {
  font-size: 24rpx;
  color: #999999;
}

/* 加载更多提示 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #999999;
}
