<!--pages/my-downloads/my-downloads.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的下载</text>
    <text class="page-subtitle">管理您的下载记录</text>
  </view>

  <!-- 筛选和排序工具栏 -->
  <view class="toolbar">
    <view class="filter-section">
      <view class="filter-item" bindtap="onGradeFilter">
        <text class="filter-label">年级</text>
        <text class="filter-value">{{filterGrade || '全部'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" bindtap="onSubjectFilter">
        <text class="filter-label">学科</text>
        <text class="filter-value">{{filterSubject || '全部'}}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" bindtap="clearFilter" wx:if="{{filterGrade || filterSubject}}">
        <text class="clear-filter">清空</text>
      </view>
    </view>
    
    <view class="sort-section">
      <picker mode="selector" range="['按时间', '按名称']" value="{{sortType === 'time' ? 0 : 1}}" bindchange="onSortChange">
        <view class="sort-picker">
          <text class="sort-label">{{sortType === 'time' ? '按时间' : '按名称'}}</text>
          <text class="sort-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{isEmpty && !loading}}">
    <image class="empty-icon" src="/images/empty-download.png" mode="aspectFit"></image>
    <text class="empty-title">暂无下载记录</text>
    <text class="empty-subtitle">快去下载一些学习资料吧</text>
    <navigator url="/pages/index/index" class="empty-button">
      <text>去首页看看</text>
    </navigator>
  </view>

  <!-- 下载列表 -->
  <view class="download-list" wx:if="{{!isEmpty && !loading}}">
    <view class="download-item" wx:for="{{downloads}}" wx:key="id">
      <view class="item-content" bindtap="viewMaterial" data-id="{{item.id}}">
        <view class="item-cover">
          <image src="{{item.cover || '/images/default-cover.png'}}" mode="aspectFill"></image>
          <view class="item-type">{{item.type}}</view>
        </view>
        
        <view class="item-info">
          <view class="item-title">{{item.title}}</view>
          <view class="item-meta">
            <text class="meta-item">{{item.grade}}</text>
            <text class="meta-separator">·</text>
            <text class="meta-item">{{item.subject}}</text>
            <text class="meta-separator">·</text>
            <text class="meta-item">{{item.size}}</text>
          </view>
          <view class="item-time">下载时间：{{item.downloadTime}}</view>
        </view>
      </view>
      
      <view class="item-actions">
        <view class="action-btn redownload-btn" bindtap="redownload" data-item="{{item}}">
          <text class="action-icon">↓</text>
          <text class="action-text">重新下载</text>
        </view>
        <view class="action-btn delete-btn" bindtap="deleteDownload" data-id="{{item.id}}">
          <text class="action-icon">×</text>
          <text class="action-text">删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-toolbar" wx:if="{{!isEmpty && !loading}}">
    <view class="toolbar-info">
      <text>共 {{downloads.length}} 条记录</text>
    </view>
    <view class="toolbar-actions">
      <view class="toolbar-btn" bindtap="clearAll">
        <text>清空全部</text>
      </view>
    </view>
  </view>
</view>