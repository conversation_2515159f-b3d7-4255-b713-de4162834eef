/* 升学专区页面样式 */

.container {
  padding-bottom: 200rpx;
  background: linear-gradient(180deg, #FF9800 0%, #FFC107 100%);
  min-height: 100vh;
}

/* 小升初专区配色 */
.container.primary-theme {
  background: linear-gradient(180deg, #4CAF50 0%, #8BC34A 100%);
}

/* 顶部区域 */
.header-section {
  padding: 60rpx 40rpx 70rpx;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 64rpx;
  font-weight: 700;
  margin-bottom: 24rpx;
  color: #FFFFFF;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.page-subtitle {
  font-size: 32rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 主要内容区域 */
.main-content {
  background-color: #F8F9FA;
  border-radius: 48rpx 48rpx 0 0;
  margin-top: 16rpx;
  padding-top: 32rpx;
  min-height: calc(100vh - 400rpx);
}

/* 筛选区域 */
.filter-section {
  padding: 0 40rpx 40rpx;
}

.filter-tabs {
  display: flex;
  background: #FFFFFF;
  border-radius: 32rpx;
  padding: 12rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  overflow-x: auto;
  white-space: nowrap;
}

.filter-tab {
  flex: 1;
  min-width: 160rpx;
  padding: 24rpx 32rpx;
  text-align: center;
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-tab.active {
  background: linear-gradient(135deg, #FF9800 0%, #FFC107 100%);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

/* 小升初专区筛选标签激活状态 */
.container.primary-theme .filter-tab.active {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.filter-tab::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

/* 小升初专区筛选标签涟漪效果 */
.container.primary-theme .filter-tab::after {
  background: radial-gradient(circle, rgba(76, 175, 80, 0.2) 0%, transparent 70%);
}

.filter-tab:active::after {
  width: 160rpx;
  height: 160rpx;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #666666;
  transition: color 0.3s ease;
  position: relative;
  z-index: 1;
}

.filter-tab.active .tab-text {
  color: #FFFFFF;
}

/* 资料列表区域 */
.material-section {
  padding: 0 40rpx 48rpx;
}

.material-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.material-item {
  display: flex;
  padding: 40rpx;
  background: linear-gradient(135deg, #FFFFFF 0%, #FAFBFF 100%);
  border-radius: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(255, 152, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 小升初专区资料卡片边框 */
.container.primary-theme .material-item {
  border: 2rpx solid rgba(76, 175, 80, 0.1);
}

.material-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: linear-gradient(180deg, #FF9800, #FFC107);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

/* 小升初专区资料卡片左边框 */
.container.primary-theme .material-item::before {
  background: linear-gradient(180deg, #4CAF50, #8BC34A);
}

.material-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 152, 0, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.8s ease, height 0.8s ease;
  pointer-events: none;
  z-index: 0;
}

.material-item:active {
  transform: translateY(-4rpx) scale(0.995);
  box-shadow: 0 16rpx 60rpx rgba(255, 152, 0, 0.15);
}

/* 小升初专区资料卡片激活状态 */
.container.primary-theme .material-item:active {
  box-shadow: 0 16rpx 60rpx rgba(76, 175, 80, 0.15);
}

.material-item:active::before {
  transform: scaleY(1);
}

.material-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

.material-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #FFF3E0 0%, #FFF8F0 100%);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 152, 0, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 小升初专区资料封面 */
.container.primary-theme .material-cover {
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8F1 100%);
  border: 4rpx solid rgba(76, 175, 80, 0.2);
}

.material-cover image {
  width: 100%;
  height: 100%;
  border-radius: 28rpx;
}

.cover-placeholder {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF9800;
  text-align: center;
}

/* 小升初专区封面占位符 */
.container.primary-theme .cover-placeholder {
  color: #4CAF50;
}

.material-item:active .material-cover {
  transform: scale(0.95);
}

.material-info {
  flex: 1;
  margin-left: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.material-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.material-item:active .material-title {
  color: #FF9800;
}

/* 小升初专区标题激活状态 */
.container.primary-theme .material-item:active .material-title {
  color: #4CAF50;
}

.material-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.material-tags .tag {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #FFF3E0 0%, #FFF8F0 100%);
  color: #FF9800;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 152, 0, 0.2);
  margin: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 小升初专区标签 */
.container.primary-theme .material-tags .tag {
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8F1 100%);
  color: #4CAF50;
  border: 2rpx solid rgba(76, 175, 80, 0.2);
}

.material-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.material-stats {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.stat-item {
  font-size: 26rpx;
  color: #999999;
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: color 0.3s ease;
}

.material-item:active .stat-item {
  color: #FF9800;
}

/* 小升初专区统计信息激活状态 */
.container.primary-theme .material-item:active .stat-item {
  color: #4CAF50;
}

.material-price {
  font-size: 32rpx;
  font-weight: 700;
  color: #FF6B35;
  background: linear-gradient(135deg, #FFE7E0 0%, #FFF2EF 100%);
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 小升初专区积分显示 */
.container.primary-theme .material-price {
  color: #2E7D32;
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8F1 100%);
  border: 2rpx solid rgba(46, 125, 50, 0.2);
}

.material-item:active .material-price {
  transform: scale(1.05);
}

/* 加载和空状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 80rpx;
  color: #999999;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .material-item {
    flex-direction: column;
    text-align: center;
  }
  
  .material-info {
    margin-left: 0;
    margin-top: 32rpx;
  }
}
