/* pages/about/about.wxss */
.about-container {
  padding: 0 32rpx 40rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}

/* 应用信息区域 */
.app-info-section {
  text-align: center;
  padding: 60rpx 0 40rpx;
  background: linear-gradient(135deg, #1677FF 0%, #4096FF 100%);
  margin: 0 -32rpx 40rpx;
  color: white;
}

.app-logo {
  margin-bottom: 24rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  background-color: rgba(255, 255, 255, 0.1);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.app-version {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 16rpx;
}

.app-description {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
  padding: 0 40rpx;
}

/* 通用区域样式 */
.features-section,
.contact-section,
.team-section,
.update-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #E6E6E6;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

/* 功能列表 */
.features-list {
  padding-left: 16rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.feature-dot {
  color: #1677FF;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

/* 联系方式 */
.contact-list {
  space-y: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-label {
  font-size: 28rpx;
  color: #666666;
  width: 140rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.contact-copy {
  font-size: 24rpx;
  color: #1677FF;
  padding: 8rpx 16rpx;
  border: 1rpx solid #1677FF;
  border-radius: 8rpx;
  background-color: transparent;
}

/* 团队介绍 */
.team-list {
  space-y: 24rpx;
}

.team-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.team-item:last-child {
  border-bottom: none;
}

.team-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #1677FF, #4096FF);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.team-info {
  flex: 1;
}

.team-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.team-role {
  font-size: 24rpx;
  color: #1677FF;
  margin-bottom: 12rpx;
}

.team-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 更新日志 */
.update-list {
  space-y: 24rpx;
}

.update-item {
  border-left: 4rpx solid #1677FF;
  padding-left: 20rpx;
}

.update-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.update-version {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.update-date {
  font-size: 24rpx;
  color: #999999;
}

.update-content {
  padding-left: 16rpx;
}

.update-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.point-dot {
  color: #1677FF;
  font-size: 20rpx;
  margin-right: 12rpx;
  margin-top: 6rpx;
}

.point-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮 */
.action-section {
  margin: 40rpx 0;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.primary-btn {
  background-color: #1677FF;
  color: white;
}

.secondary-btn {
  background-color: #FFFFFF;
  color: #333333;
  border: 2rpx solid #E6E6E6;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-weight: 500;
}

/* 版权信息 */
.copyright-section {
  text-align: center;
  padding: 40rpx 0 20rpx;
  border-top: 1rpx solid #F0F0F0;
  margin-top: 40rpx;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.6;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .app-info-section {
    background: linear-gradient(135deg, #0958D9 0%, #1677FF 100%);
  }
  
  .logo-image {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .contact-copy {
    background-color: #FFFFFF;
  }
  
  .secondary-btn {
    background-color: #F8F9FA;
  }
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .about-container {
    padding: 0 24rpx 40rpx;
  }
  
  .app-info-section {
    margin: 0 -24rpx 40rpx;
    padding: 50rpx 24rpx 40rpx;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 16rpx;
  }
}