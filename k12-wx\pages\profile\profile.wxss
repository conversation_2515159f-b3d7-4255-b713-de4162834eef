/* pages/profile/profile.wxss */

.container {
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息头部 - 现代化设计 */
.user-header {
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
  padding: 60rpx 32rpx 40rpx;
  margin-bottom: 24rpx;
  position: relative;
  overflow: hidden;
}

.user-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
  pointer-events: none;
}

.user-info {
  display: flex;
  align-items: center;
  color: #FFFFFF;
  position: relative;
  z-index: 1;
}

.avatar-wrapper {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(10px);
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* 头像涟漪效果 */
.avatar-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.avatar-wrapper:active {
  transform: scale(0.95);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.avatar-wrapper:active::after {
  width: 200rpx;
  height: 200rpx;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-icon {
  font-size: 56rpx;
  color: rgba(255, 255, 255, 0.9);
}

.user-details {
  flex: 1;
}

.username {
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.85;
  font-weight: 400;
}

.user-points {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 24rpx 20rpx;
  min-width: 140rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 积分区域涟漪效果 */
.user-points::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.user-points:active {
  transform: scale(0.95);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.user-points:active::after {
  width: 200rpx;
  height: 200rpx;
}

.points-value {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  animation: countUp 1s ease-out;
}

/* 数字滚动动画 */
@keyframes countUp {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-5rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.points-label {
  font-size: 22rpx;
  opacity: 0.85;
  font-weight: 500;
}

.arrow-icon {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 300;
  margin-left: 8rpx;
}

/* 用户统计区域 */
.stats-section {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 24rpx 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0;
}

.stats-item {
  flex: 1;
  text-align: center;
  position: relative;
  padding: 16rpx 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16rpx;
  overflow: hidden;
  max-width: 200rpx;
}

.stats-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.stats-item:active {
  background: rgba(22, 119, 255, 0.05);
  transform: scale(0.95);
}

.stats-item:active::after {
  width: 120rpx;
  height: 120rpx;
}

.stats-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #1677FF;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
  animation: countUp 1s ease-out;
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* 统计项分隔线 */
.stats-item:not(:last-child)::before {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1rpx;
  background: linear-gradient(180deg, transparent 0%, rgba(22, 119, 255, 0.2) 50%, transparent 100%);
}

/* 菜单区域 - 现代卡片设计 */
.menu-section,
.settings-section {
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 24rpx 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 菜单项涟漪效果 */
.menu-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: linear-gradient(90deg, rgba(22, 119, 255, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
  transform: translateX(8rpx);
}

.menu-item:active::before {
  width: 6rpx;
}

.menu-item:active::after {
  width: 400rpx;
  height: 400rpx;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.menu-item:active .menu-icon {
  transform: scale(1.1);
  box-shadow: 0 6rpx 20rpx rgba(22, 119, 255, 0.4);
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.menu-value {
  font-size: 26rpx;
  color: #8a8a8a;
  margin-right: 16rpx;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.menu-badge {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 600;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
  position: relative;
  z-index: 1;
}

/* 退出登录 - 现代化按钮 */
.logout-section {
  margin: 40rpx 24rpx 0;
}

.logout-btn {
  background: #ffffff;
  color: #FF6B35;
  font-size: 30rpx;
  font-weight: 600;
  text-align: center;
  padding: 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
  transition: left 0.5s;
}

/* 退出按钮涟漪效果 */
.logout-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.logout-btn:active {
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
  transform: scale(0.98);
  border-color: rgba(255, 107, 53, 0.4);
}

.logout-btn:active::before {
  left: 100%;
}

.logout-btn:active::after {
  width: 300rpx;
  height: 300rpx;
}

/* 动画效果增强 */
.menu-section,
.settings-section {
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-header {
  animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 菜单项依次出现动画 */
.menu-item:nth-child(1) { animation-delay: 0.1s; }
.menu-item:nth-child(2) { animation-delay: 0.15s; }
.menu-item:nth-child(3) { animation-delay: 0.2s; }
.menu-item:nth-child(4) { animation-delay: 0.25s; }
.menu-item:nth-child(5) { animation-delay: 0.3s; }

.menu-item {
  animation: fadeInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .user-header {
    padding: 50rpx 24rpx 32rpx;
  }
  
  .avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
    margin-right: 24rpx;
  }
  
  .username {
    font-size: 36rpx;
  }
  
  .user-points {
    min-width: 120rpx;
    padding: 20rpx 16rpx;
  }
  
  .points-value {
    font-size: 32rpx;
  }
  
  .menu-item {
    padding: 28rpx 24rpx;
  }
  
  .menu-text {
    font-size: 28rpx;
  }
}

/* 响应式适配 - 统计区域 */
@media (max-width: 750rpx) {
  .stats-section {
    margin: 0 16rpx 16rpx;
    padding: 24rpx 16rpx;
  }
  
  .stats-value {
    font-size: 40rpx;
  }
  
  .stats-label {
    font-size: 22rpx;
  }
  
  .stats-item {
    padding: 12rpx 4rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%);
  }
  
  .menu-section,
  .settings-section,
  .stats-section,
  .logout-btn {
    background: #2d2d2d;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .menu-text {
    color: #ffffff;
  }
  
  .menu-value {
    color: #a0a0a0;
  }
  
  .stats-label {
    color: #a0a0a0;
  }
  
  .menu-item {
    background: #2d2d2d;
    border-bottom-color: rgba(255, 255, 255, 0.08);
  }
  
  .menu-item:active {
    background: linear-gradient(90deg, rgba(22, 119, 255, 0.15) 0%, rgba(45, 45, 45, 1) 100%);
  }
  
  .stats-item:active {
    background: rgba(22, 119, 255, 0.15);
  }
}
