// pages/points-history/points-history.js
const app = getApp()

Page({
  data: {
    records: [],
    loading: false,
    isEmpty: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    filterType: '', // 筛选类型：'' 全部, 'earn' 获得, 'spend' 消费
    stats: {
      totalEarned: 0,
      totalSpent: 0,
      currentBalance: 0
    },
    typeOptions: [
      { value: '', label: '全部记录' },
      { value: 'register', label: '注册奖励' },
      { value: 'share', label: '分享获得' },
      { value: 'ad', label: '观看广告' },
      { value: 'daily', label: '每日签到' },
      { value: 'download', label: '下载消费' },
      { value: 'invite', label: '邀请奖励' }
    ]
  },

  onLoad(options) {
    this.loadPointsHistory()
  },

  onShow() {
    // 如果是从其他页面返回，刷新数据
    if (this.data.records.length > 0) {
      this.refreshData()
    }
  },

  // 加载积分记录
  async loadPointsHistory(refresh = false) {
    if (this.data.loading) return
    
    // 如果是刷新，重置分页
    if (refresh) {
      this.setData({
        page: 1,
        records: [],
        hasMore: true
      })
    }
    
    this.setData({ loading: true })
    
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      const result = await app.api.points.getPointsHistory({
        userId: userInfo._id,
        page: this.data.page,
        pageSize: this.data.pageSize,
        type: this.data.filterType
      })
      
      let newRecords = refresh ? result.records : [...this.data.records, ...result.records]
      
      this.setData({
        records: newRecords,
        isEmpty: newRecords.length === 0,
        hasMore: result.hasMore,
        page: refresh ? 2 : this.data.page + 1,
        stats: result.stats,
        loading: false
      })
      
    } catch (error) {
      console.error('加载积分记录失败:', error)
      
      this.setData({
        loading: false,
        isEmpty: true
      })
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      })
    }
  },

  // 刷新数据
  refreshData() {
    this.loadPointsHistory(true)
  },

  // 类型筛选
  onTypeFilter() {
    wx.showActionSheet({
      itemList: this.data.typeOptions.map(item => item.label),
      success: (res) => {
        const selectedType = this.data.typeOptions[res.tapIndex].value
        this.setData({ filterType: selectedType })
        this.refreshData()
      }
    })
  },

  // 清空筛选
  clearFilter() {
    this.setData({ filterType: '' })
    this.refreshData()
  },

  // 获取记录类型显示文本
  getTypeText(type) {
    const typeMap = {
      'register': '注册奖励',
      'share': '分享获得',
      'ad': '观看广告',
      'daily': '每日签到',
      'download': '下载消费',
      'invite': '邀请奖励'
    }
    return typeMap[type] || '其他'
  },

  // 获取积分显示样式
  getPointsClass(points) {
    return points > 0 ? 'points-earn' : 'points-spend'
  },

  // 格式化时间
  formatTime(timeStr) {
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前'
    } else if (diff < 2592000000) { // 30天内
      return Math.floor(diff / 86400000) + '天前'
    } else {
      return time.toLocaleDateString()
    }
  },

  // 查看积分规则
  viewPointsRules() {
    wx.showModal({
      title: '积分规则',
      content: '• 注册奖励：100积分\n• 每日签到：5积分\n• 分享资料：10积分\n• 观看广告：2积分\n• 邀请好友：50积分\n• 下载资料：消费10-50积分',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 赚取积分
  earnPoints() {
    wx.navigateTo({
      url: '/pages/earn-points/earn-points'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadPointsHistory()
    }
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '积分明细 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})