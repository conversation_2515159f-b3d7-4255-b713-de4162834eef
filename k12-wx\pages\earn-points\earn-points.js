// pages/earn-points/earn-points.js
const app = getApp()

Page({
  data: {
    userPoints: 0,
    earnMethods: [
      {
        id: 1,
        name: '每日签到',
        desc: '每天签到获得积分奖励',
        icon: '📅',
        points: 10,
        status: 'available',
        statusText: '去签到'
      },
      {
        id: 2,
        name: '分享小程序',
        desc: '分享小程序给好友',
        icon: '📱',
        points: 5,
        status: 'available',
        statusText: '去分享'
      },
      {
        id: 3,
        name: '邀请用户使用',
        desc: '邀请好友注册使用',
        icon: '👥',
        points: 20,
        status: 'available',
        statusText: '去邀请'
      },
      {
        id: 4,
        name: '观看广告',
        desc: '观看激励视频广告',
        icon: '📺',
        points: 2,
        status: 'unavailable',
        statusText: '暂未开放'
      }
    ]
  },

  onLoad: function() {
    this.loadUserPoints()
    this.loadEarnMethodsStatus()
  },

  onShow: function() {
    this.loadUserPoints()
    this.loadEarnMethodsStatus()
  },

  // 加载用户积分
  async loadUserPoints() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，从云数据库获取用户积分
        try {
          const result = await app.api.user.getUserStats(userInfo.openid)
          
          if (result.success && result.data) {
            this.setData({
              userPoints: result.data.points || 0
            })
          } else {
            // 云数据库获取失败，使用本地存储
            this.loadLocalUserPoints()
          }
        } catch (error) {
          console.error('从云数据库获取用户积分失败:', error)
          // 降级到本地存储
          this.loadLocalUserPoints()
        }
      } else {
        // 未登录，使用本地存储
        this.loadLocalUserPoints()
      }
      
    } catch (error) {
      console.error('加载用户积分失败:', error)
      this.loadLocalUserPoints()
    }
  },

  // 从本地存储加载用户积分（降级方案）
  loadLocalUserPoints() {
    const points = app.getUserPoints()
    this.setData({
      userPoints: points
    })
  },

  // 加载积分获取方式状态
  async loadEarnMethodsStatus() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      const today = new Date().toDateString()
      
      if (userInfo.openid) {
        // 如果已登录，从云数据库获取状态
        try {
          await this.loadCloudEarnMethodsStatus(userInfo.openid, today)
        } catch (error) {
          console.error('从云数据库加载状态失败:', error)
          // 降级到本地存储
          this.loadLocalEarnMethodsStatus(today)
        }
      } else {
        // 未登录，使用本地存储
        this.loadLocalEarnMethodsStatus(today)
      }
      
    } catch (error) {
      console.error('加载积分获取方式状态失败:', error)
      this.loadLocalEarnMethodsStatus(new Date().toDateString())
    }
  },

  // 从云数据库加载积分获取方式状态
  async loadCloudEarnMethodsStatus(userId, today) {
    try {
      // 获取今天的积分记录
      const result = await app.api.points.getPointsHistory({
        userId: userId,
        page: 1,
        pageSize: 50
      })
      
      let todayRecords = []
      let allRecords = []
      
      if (result.success && result.data && result.data.records) {
        allRecords = result.data.records
        todayRecords = result.data.records.filter(record => {
          const recordDate = new Date(record.createTime).toDateString()
          return recordDate === today
        })
      }
      
      // 检查各种任务完成状态
      const hasSignedToday = todayRecords.some(record => record.action === 'signin')
      
      const earnMethods = this.data.earnMethods.map(method => {
        let status = 'available'
        let statusText = '去完成'
        
        if (method.id === 1) {
          // 每日签到
          if (hasSignedToday) {
            status = 'completed'
            statusText = '已签到'
          } else {
            statusText = '去签到'
          }
        } else if (method.id === 2) {
          // 分享小程序
          statusText = '去分享'
        } else if (method.id === 3) {
          // 邀请用户使用
          statusText = '去邀请'
        } else if (method.id === 4) {
          // 观看广告
          status = 'unavailable'
          statusText = '暂未开放'
        }
        
        return {
          ...method,
          status: status,
          statusText: statusText
        }
      })
      
      this.setData({
        earnMethods: earnMethods
      })
      
    } catch (error) {
      throw error
    }
  },

  // 从本地存储加载积分获取方式状态（降级方案）
  loadLocalEarnMethodsStatus(today) {
    try {
      const completedTasks = wx.getStorageSync('completedTasks') || {}
      
      const earnMethods = this.data.earnMethods.map(method => {
        let status = 'available'
        let statusText = '去完成'
        
        if (method.id === 1) {
          // 每日签到
          if (completedTasks['signin_' + today]) {
            status = 'completed'
            statusText = '已签到'
          } else {
            statusText = '去签到'
          }
        } else if (method.id === 2) {
          // 分享小程序
          statusText = '去分享'
        } else if (method.id === 3) {
          // 邀请用户使用
          statusText = '去邀请'
        } else if (method.id === 4) {
          // 观看广告
          status = 'unavailable'
          statusText = '暂未开放'
        }
        
        return {
          ...method,
          status: status,
          statusText: statusText
        }
      })
      
      this.setData({
        earnMethods: earnMethods
      })
      
    } catch (error) {
      console.error('从本地存储加载状态失败:', error)
    }
  },

  // 处理积分获取方式点击
  handleEarnMethod: function(e) {
    var method = e.currentTarget.dataset.method
    
    if (method.status === 'completed') {
      wx.showToast({
        title: '已完成',
        icon: 'none'
      })
      return
    }
    
    if (method.status === 'unavailable') {
      wx.showToast({
        title: '功能暂未开放',
        icon: 'none'
      })
      return
    }
    
    if (method.id === 1) {
      // 每日签到
      this.handleSignIn(method)
    } else if (method.id === 2) {
      // 分享小程序
      this.handleShare(method)
    } else if (method.id === 3) {
      // 邀请用户使用
      this.handleInvite(method)
    } else if (method.id === 4) {
      // 观看广告
      this.handleWatchAd(method)
    }
  },

  // 处理每日签到
  async handleSignIn(method) {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，使用云数据库签到
        try {
          const result = await app.api.points.signIn(userInfo.openid)
          
          if (result.success) {
            wx.showToast({
              title: `签到成功，获得${result.data.points}积分`,
              icon: 'success',
              duration: 2000
            })
            
            // 重新加载积分和状态
            this.loadUserPoints()
            this.loadEarnMethodsStatus()
          } else {
            wx.showToast({
              title: result.message || '签到失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('云数据库签到失败:', error)
          // 降级到本地签到
          this.handleLocalSignIn(method)
        }
      } else {
        // 未登录，使用本地签到
        this.handleLocalSignIn(method)
      }
      
    } catch (error) {
      console.error('签到失败:', error)
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none'
      })
    }
  },

  // 本地签到（降级方案）
  handleLocalSignIn(method) {
    try {
      const today = new Date().toDateString()
      const completedTasks = wx.getStorageSync('completedTasks') || {}
      
      if (completedTasks['signin_' + today]) {
        wx.showToast({
          title: '今天已经签到过了',
          icon: 'none'
        })
        return
      }
      
      // 标记签到完成
      completedTasks['signin_' + today] = true
      wx.setStorageSync('completedTasks', completedTasks)
      
      // 奖励积分
      this.rewardLocalPoints(method.points, '每日签到')
      
      // 更新状态
      this.loadEarnMethodsStatus()
      
    } catch (error) {
      console.error('本地签到失败:', error)
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none'
      })
    }
  },

  // 处理分享小程序
  async handleShare(method) {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，使用云数据库记录分享
        try {
          const result = await app.api.points.shareForPoints(userInfo.openid, {
            shareType: 'miniprogram'
          })
          
          if (result.success) {
            wx.showToast({
              title: `分享成功，获得${result.data.points}积分`,
              icon: 'success',
              duration: 2000
            })
            
            // 重新加载积分
            this.loadUserPoints()
          } else {
            wx.showToast({
              title: result.message || '分享失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('云数据库分享失败:', error)
          // 降级到本地分享
          this.rewardLocalPoints(method.points, '分享小程序')
        }
      } else {
        // 未登录，使用本地分享
        this.rewardLocalPoints(method.points, '分享小程序')
      }
      
    } catch (error) {
      console.error('分享失败:', error)
    }
  },

  // 处理邀请用户
  handleInvite(method) {
    wx.showModal({
      title: '邀请好友',
      content: '分享小程序给好友，好友注册后您将获得100积分奖励',
      confirmText: '立即分享',
      success: (res) => {
        if (res.confirm) {
          this.handleShare(method)
        }
      }
    })
  },


  // 处理观看广告
  async handleWatchAd(method) {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，使用云数据库记录观看广告
        try {
          const result = await app.api.points.watchAdForPoints(userInfo.openid)
          
          if (result.success) {
            wx.showToast({
              title: `观看成功，获得${result.data.points}积分`,
              icon: 'success',
              duration: 2000
            })
            
            // 重新加载积分
            this.loadUserPoints()
          } else {
            wx.showToast({
              title: result.message || '观看失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('云数据库观看广告失败:', error)
          // 降级到本地奖励
          this.rewardLocalPoints(method.points, '观看广告')
        }
      } else {
        // 未登录，使用本地奖励
        this.rewardLocalPoints(method.points, '观看广告')
      }
      
    } catch (error) {
      console.error('观看广告失败:', error)
    }
  },

  // 本地积分奖励（降级方案）
  rewardLocalPoints(points, reason) {
    const currentPoints = app.getUserPoints()
    const newPoints = currentPoints + points
    app.updateUserPoints(newPoints)
    
    this.setData({
      userPoints: newPoints
    })
    
    wx.showToast({
      title: reason + '成功，获得' + points + '积分',
      icon: 'success',
      duration: 2000
    })
  },

  // 分享页面
  onShareAppMessage() {
    // 分享成功后奖励积分
    setTimeout(() => {
      this.handleShareReward()
    }, 1000)
    
    return {
      title: 'K12教育资料库 - 海量学习资料免费下载',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 处理分享奖励
  async handleShareReward() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || {}
      
      if (userInfo.openid) {
        // 如果已登录，使用云数据库记录分享
        try {
          const result = await app.api.points.shareForPoints(userInfo.openid, {
            shareType: 'appMessage'
          })
          
          if (result.success) {
            wx.showToast({
              title: `分享成功，获得${result.data.points}积分`,
              icon: 'success',
              duration: 2000
            })
            
            // 重新加载积分
            this.loadUserPoints()
          }
        } catch (error) {
          console.error('云数据库分享奖励失败:', error)
          // 降级到本地奖励
          this.rewardLocalPoints(50, '分享小程序')
        }
      } else {
        // 未登录，使用本地奖励
        this.rewardLocalPoints(50, '分享小程序')
      }
      
    } catch (error) {
      console.error('分享奖励失败:', error)
    }
  }
})