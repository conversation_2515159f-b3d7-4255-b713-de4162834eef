# 如何获取腾讯云 API 密钥 (SecretId 和 SecretKey)

腾讯云 API 密钥是访问腾讯云服务的重要凭证，由 SecretId 和 SecretKey 组成。为了确保后台管理系统能够安全地调用云开发的接口，您需要获取并配置这些密钥。

请按照以下步骤操作：

## 第一步：登录腾讯云控制台

首先，请打开浏览器并访问 [腾讯云控制台](https://console.cloud.tencent.com/)，然后使用您的腾讯云账号登录。

## 第二步：进入 API 密钥管理页面

登录成功后，您可以通过以下两种方式进入 API 密钥管理页面：

1.  **直接访问链接**：
    点击此链接可直接跳转到 API 密钥管理页面：
    [https://console.cloud.tencent.com/cam/capi](https://console.cloud.tencent.com/cam/capi)

2.  **手动导航**：
    a. 在控制台顶部的搜索框中输入“**访问管理**”并按回车键。
    b. 在访问管理（CAM）页面的左侧导航栏中，选择【**访问密钥**】 -> 【**API 密钥管理**】。

## 第三步：创建或查看 API 密钥

在 API 密钥管理页面，您可以：

-   **查看现有密钥**：如果您之前已经创建过密钥，它们会在此页面列出。您可以直接复制 `SecretId`。`SecretKey` 通常只在创建时显示一次，如果您忘记了，建议新建一个密钥。

-   **新建密钥**：
    a. 点击【**新建密钥**】按钮。
    b. 系统可能会要求您进行安全验证（例如，通过短信验证码或 MFA）。
    c. 验证通过后，系统将生成一个新的 `SecretId` 和 `SecretKey`。

## 第四步：保存您的密钥

**非常重要**：

-   `SecretKey` **只在创建时显示一次**。请务必立即复制并将其保存在一个安全的地方。如果关闭了弹窗，您将无法再次查看它。
-   请将复制的 `SecretId` 和 `SecretKey` 提供给开发人员或在下一步中配置到后台管理系统的环境变量中。

---

获取密钥后，请回到开发流程，并将它们提供给我，以便我继续完成后台管理系统的配置。