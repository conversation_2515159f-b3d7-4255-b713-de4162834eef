import cloudbase from '@cloudbase/js-sdk'

const app = cloudbase.init({
  env: 'cloud1-8gm001v7fd56ff43'
})

const auth = app.auth()
const db = app.database()

// 全局认证状态标记
let isAuthInitialized = false

// 确保认证状态 - 修复版本，避免重复创建认证对象
const ensureAuth = async () => {
  try {
    console.log('🔥 ensureAuth: 开始认证检查，当前状态:', isAuthInitialized)
    
    // 如果已经初始化过认证，直接返回成功
    if (isAuthInitialized) {
      console.log('🔥 ensureAuth: 认证已初始化，直接返回')
      return true
    }
    
    const loginState = await auth.getLoginState()
    console.log('🔥 ensureAuth: 当前登录状态:', loginState)
    
    if (!loginState) {
      console.log('🔥 ensureAuth: 未登录，开始匿名登录')
      // 只在未登录时进行匿名登录
      const result = await auth.signInAnonymously()
      console.log('🔥 ensureAuth: 匿名登录结果:', result)
    }
    
    // 获取最新的登录状态
    const newLoginState = await auth.getLoginState()
    console.log('🔥 ensureAuth: 最新登录状态:', newLoginState)
    
    // 标记认证已初始化
    isAuthInitialized = true
    return true
  } catch (error) {
    console.error('🔥 ensureAuth: 认证失败:', error)
    return false
  }
}

// 配置类型定义
export type ConfigType = 'basic' | 'features' | 'business' | 'storage' | 'security'

export interface SystemConfig {
  _id?: string
  configType: ConfigType
  configData: any
  updateTime: Date
}

// 获取所有系统配置
export const getSystemConfigs = async (): Promise<Record<ConfigType, any>> => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      throw new Error('认证失败')
    }
    
    const result = await db.collection('system_configs').get()
    
    const configs: Record<ConfigType, any> = {
      basic: {},
      features: {},
      business: {},
      storage: {},
      security: {}
    }
    
    result.data.forEach((config: any) => {
      if (config.configType && configs[config.configType as ConfigType] !== undefined) {
        configs[config.configType as ConfigType] = config.configData || {}
      }
    })
    
    // 设置默认值
    if (Object.keys(configs.basic).length === 0) {
      configs.basic = {
        miniProgramName: 'K12教育资源小程序',
        miniProgramVersion: '1.0.0',
        adminEmail: '<EMAIL>',
        supportPhone: '************',
        miniProgramDescription: '专为K12教育机构设计的资源管理小程序，提供丰富的教育资源下载和管理功能。',
        recommendedContent: '最新教学资源已上线，欢迎下载使用！',
        miniProgramEnabled: true,
        maintenanceMode: false,
        maintenanceMessage: '系统正在维护中，预计30分钟后恢复正常，给您带来不便敬请谅解。'
      }
    }
    
    if (Object.keys(configs.features).length === 0) {
      configs.features = {
        watchAdEnabled: true,
        shareEnabled: true,
        favoriteEnabled: true,
        registerEnabled: true,
        commentEnabled: true,
        searchEnabled: true
      }
    }
    
    if (Object.keys(configs.business).length === 0) {
      configs.business = {
        previewPages: 3,
        defaultPointPrice: 5,
        maxFileSize: 50,
        dailyPointLimit: 100,
        registerBonus: 20,
        shareBonus: 5,
        adBonus: 2,
        dailySignBonus: 1
      }
    }
    
    if (Object.keys(configs.storage).length === 0) {
      configs.storage = {
        storageType: 'cloud',
        maxStorageSize: '100',
        maxFileSize: '50',
        allowedFileTypes: ['pdf', 'doc,docx', 'xls,xlsx', 'jpg,jpeg,png,gif'],
        autoCleanup: true,
        cleanupDays: '30'
      }
    }
    
    if (Object.keys(configs.security).length === 0) {
      configs.security = {
        debugMode: true,
        consoleLog: true,
        apiLog: true,
        showErrorDetails: true,
        devNotes: '本地开发环境，用于功能开发和测试'
      }
    }
    
    return configs
  } catch (error) {
    console.error('获取系统配置失败:', error)
    throw error
  }
}

// 更新系统配置
export const updateSystemConfig = async (configType: ConfigType, configData: any): Promise<void> => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      throw new Error('认证失败')
    }
    
    // 查找是否已存在该类型的配置
    const existingResult = await db.collection('system_configs')
      .where({
        configType: configType
      })
      .get()
    
    const updateData = {
      configType,
      configData,
      updateTime: new Date()
    }
    
    if (existingResult.data.length > 0) {
      // 更新现有配置
      await db.collection('system_configs')
        .doc(existingResult.data[0]._id)
        .update(updateData)
    } else {
      // 创建新配置
      await db.collection('system_configs').add(updateData)
    }
  } catch (error) {
    console.error('更新系统配置失败:', error)
    throw error
  }
}

// 获取单个配置
export const getSystemConfig = async (configType: ConfigType): Promise<any> => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      throw new Error('认证失败')
    }
    
    const result = await db.collection('system_configs')
      .where({
        configType: configType
      })
      .get()
    
    if (result.data.length > 0) {
      return result.data[0].configData
    }
    
    return {}
  } catch (error) {
    console.error('获取系统配置失败:', error)
    throw error
  }
}

// 重置配置为默认值
export const resetSystemConfig = async (configType: ConfigType): Promise<void> => {
  try {
    // 确保认证状态
    const authSuccess = await ensureAuth()
    if (!authSuccess) {
      throw new Error('认证失败')
    }
    
    let defaultConfig: any = {}
    
    switch (configType) {
      case 'basic':
        defaultConfig = {
          miniProgramName: 'K12教育资源小程序',
          miniProgramVersion: '1.0.0',
          adminEmail: '<EMAIL>',
          supportPhone: '************',
          miniProgramDescription: '专为K12教育机构设计的资源管理小程序，提供丰富的教育资源下载和管理功能。',
          recommendedContent: '最新教学资源已上线，欢迎下载使用！',
          miniProgramEnabled: true,
          maintenanceMode: false,
          maintenanceMessage: '系统正在维护中，预计30分钟后恢复正常，给您带来不便敬请谅解。'
        }
        break
      case 'features':
        defaultConfig = {
          watchAdEnabled: true,
          shareEnabled: true,
          favoriteEnabled: true,
          registerEnabled: true,
          commentEnabled: true,
          searchEnabled: true
        }
        break
      case 'business':
        defaultConfig = {
          previewPages: 3,
          defaultPointPrice: 5,
          maxFileSize: 50,
          dailyPointLimit: 100,
          registerBonus: 20,
          shareBonus: 5,
          adBonus: 2,
          dailySignBonus: 1
        }
        break
      case 'storage':
        defaultConfig = {
          storageType: 'cloud',
          maxStorageSize: '100',
          maxFileSize: '50',
          allowedFileTypes: ['pdf', 'doc,docx', 'xls,xlsx', 'jpg,jpeg,png,gif'],
          autoCleanup: true,
          cleanupDays: '30'
        }
        break
      case 'security':
        defaultConfig = {
          debugMode: true,
          consoleLog: true,
          apiLog: true,
          showErrorDetails: true,
          devNotes: '本地开发环境，用于功能开发和测试'
        }
        break
    }
    
    await updateSystemConfig(configType, defaultConfig)
  } catch (error) {
    console.error('重置系统配置失败:', error)
    throw error
  }
}