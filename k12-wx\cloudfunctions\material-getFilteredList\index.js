// 云函数入口文件 - 专门用于筛选页面的资料列表获取
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { 
    grade = '', 
    subject = '', 
    volume = '',
    section = '',
    keyword = '', 
    page = 1, 
    pageSize = 20,
    sortType = 'download',
    sortOrder = 'desc',
    excludeUpgrade = false
  } = event
  
  try {
    console.log('筛选页面接收到的参数:', event)
    
    // 构建查询条件
    let whereCondition = {
      status: 'active'  // 只查询已上架的资料
    }
    
    // 如果需要排除升学专区数据
    if (excludeUpgrade) {
      whereCondition.upgradeType = db.command.or([
        db.command.exists(false),
        db.command.eq(null),
        db.command.eq('')
      ])
    }
    
    // 筛选条件 - 专门为筛选页面优化
    if (grade) {
      whereCondition.grade = grade
      console.log('添加年级筛选:', grade)
    }
    
    if (subject) {
      whereCondition.subject = subject
      console.log('添加科目筛选:', subject)
    }
    
    if (volume) {
      whereCondition.volume = volume
      console.log('添加册别筛选:', volume)
    }
    
    if (section) {
      whereCondition.section = section
      console.log('添加板块筛选:', section)
    }
    
    if (keyword) {
      whereCondition.title = db.RegExp({
        regexp: keyword,
        options: 'i'
      })
      console.log('添加关键词筛选:', keyword)
    }
    
    console.log('最终查询条件:', whereCondition)
    
    // 计算跳过的记录数
    const skip = (page - 1) * pageSize
    
    // 查询资料列表
    let materialsQuery = db.collection('files').where(whereCondition)
    
    // 排序逻辑
    if (sortType === 'download') {
      materialsQuery = materialsQuery.orderBy('download_count', sortOrder)
    } else if (sortType === 'time') {
      materialsQuery = materialsQuery.orderBy('updated_time', sortOrder)
    } else {
      // 默认按更新时间排序
      materialsQuery = materialsQuery.orderBy('updated_time', sortOrder)
    }
    
    // 执行查询
    const materialsResult = await materialsQuery
      .skip(skip)
      .limit(pageSize)
      .field({
        title: true,
        grade: true,
        subject: true,
        section: true,
        volume: true,
        type: true,
        file_type: true,
        file_size: true,
        size: true,
        download_count: true,
        downloads: true,
        view_count: true,
        share_count: true,
        favorite_count: true,
        preview_count: true,
        points: true,
        preview_images: true,
        upgradeType: true,
        upgradeCategory: true,
        createTime: true,
        updateTime: true,
        upload_time: true,
        updated_time: true,
        fileID: true,
        cloudPath: true,
        file_url: true,
        thumbnail_url: true,
        status: true,
        tags: true,
        description: true,
        is_featured: true
      })
      .get()
    
    console.log('筛选查询结果数量:', materialsResult.data.length)
    
    // 获取总数
    const countResult = await db.collection('files')
      .where(whereCondition)
      .count()
    
    console.log('筛选结果总数:', countResult.total)
    
    // 处理数据格式，适配前端需求
    const processedData = materialsResult.data.map(item => ({
      id: item._id,
      title: item.title,
      grade: item.grade,
      subject: item.subject,
      section: item.section,
      volume: item.volume,
      type: item.type,
      fileSize: item.size,
      downloadCount: item.download_count || item.downloads || 0,
      viewCount: item.view_count || 0,
      shareCount: item.share_count || 0,
      previewCount: item.preview_count || 0,
      points: item.points || 0,
      previewImages: item.preview_images || [],
      upgradeType: item.upgradeType || null,
      upgradeCategory: item.upgradeCategory || null,
      createTime: item.createTime,
      updateTime: item.updateTime,
      fileID: item.fileID,
      cloudPath: item.cloudPath,
      status: item.status,
      // 生成标签
      tags: [item.grade, item.subject, item.section].filter(Boolean)
    }))
    
    // 计算分页信息
    const totalCount = countResult.total
    const totalPages = Math.ceil(totalCount / pageSize)
    const hasMore = page < totalPages
    
    return {
      success: true,
      data: processedData,
      total: totalCount,
      page: page,
      pageSize: pageSize,
      hasMore: hasMore,
      message: '筛选获取成功',
      filterInfo: {
        grade,
        subject,
        volume,
        section,
        keyword
      }
    }
    
  } catch (error) {
    console.error('筛选页面获取资料列表失败:', error)
    return {
      success: false,
      message: '筛选获取失败: ' + error.message,
      error: error.message,
      data: [],
      total: 0,
      page: 1,
      pageSize: 20,
      hasMore: false
    }
  }
}