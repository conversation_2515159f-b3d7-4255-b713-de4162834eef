# 集合权限配置说明

本文档用于说明小程序云数据库中各个集合的权限配置。权限配置基于微信云开发提供的标准权限模型。

## 核心原则：区分小程序端与服务端权限

**需要特别注意：** 此处定义的所有权限规则，**仅对从微信小程序客户端发起的请求有效**。

**云控制台和后端管理系统（服务端）始终拥有所有数据的最高读写权限**，不受这些规则的限制。这种设计是标准实践，旨在：
1.  **保护用户数据安全**：在小程序端，严格限制用户只能访问其自身数据或公共数据。
2.  **确保管理后台功能**：在服务端，允许管理员进行数据管理、内容审核、系统维护等所有操作。

因此，当前权限设计是正确的，因为它在保障小程序用户数据隔离的同时，也为后台管理系统保留了必要的管理能力。

## 权限选项

根据业务场景，我们采用以下几种权限模式：

1.  **所有用户可读，仅创建者可读写**: 适用于公开信息，但只有创建者能修改的场景（如用户评论）。
2.  **仅创建者可读写**: 适用于用户的私有数据，只有用户自己能访问（如个人设置、订单）。
3.  **所有用户可读**: 适用于公共数据，所有用户都能查看（如商品信息、公告）。
4.  **所有用户不可读写**: 适用于仅后台可访问的数据（如后台流水）。

## 各集合权限配置

以下是本项目中8个核心集合的权限配置建议：

### 1. users (用户表)
- **权限选择**: **仅创建者可读写**
- **说明**: 用户信息是私密的，每个用户只能读取和修改自己的数据。`openid` 会作为记录的 `_openid`，云数据库会自动处理创建者身份。

### 2. files (文件表)
- **权限选择**: **所有用户可读**
- **说明**: 文件资料信息需要对所有用户可见，以便浏览和搜索。文件的创建和修改由管理后台完成，小程序端用户只有读取权限。

### 3. categories (分类表)
- **权限选择**: **所有用户可读**
- **说明**: 分类信息是公共数据，所有用户都需要读取以进行筛选和导航。

### 4. point_records (积分记录表)
- **权限选择**: **仅创建者可读写**
- **说明**: 用户的积分记录属于个人隐私，只有用户本人可以查看。

### 5. downloads (下载记录表)
- **权限选择**: **仅创建者可读写**
- **说明**: 用户的下载历史是个人行为数据，应设为私有，只有用户本人可查看。

### 6. favorites (收藏表)
- **权限选择**: **仅创建者可读写**
- **说明**: 用户的收藏列表是个人数据，只有用户本人可以查看和管理。

### 7. shares (分享记录表)
- **权限选择**: **仅创建者可读写**
- **说明**: 用户的分享记录属于个人行为数据，应设为私有。

### 8. system_configs (系统配置表)
- **权限选择**: **所有用户可读**
- **说明**: 系统配置项（如应用名称、积分规则说明等）需要被小程序前端读取以正常展示和运行。该表由管理后台维护，小程序端只读。