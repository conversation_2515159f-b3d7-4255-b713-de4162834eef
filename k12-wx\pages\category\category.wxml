<view class="container">
  <!-- 筛选器区域 -->
  <view class="filter-section">
    <view class="filter-row">
      <view class="filter-item {{selectedGrade ? 'selected' : ''}}" bindtap="showGradeFilter">
        <text class="filter-text">{{selectedGrade || '年级'}}</text>
        <text class="filter-arrow {{showGradePopup ? 'rotate' : ''}}">▼</text>
      </view>
      <view class="filter-item {{selectedSubject ? 'selected' : ''}}" bindtap="showSubjectFilter">
        <text class="filter-text">{{selectedSubject || '科目'}}</text>
        <text class="filter-arrow {{showSubjectPopup ? 'rotate' : ''}}">▼</text>
      </view>
    </view>
    <view class="filter-row">
      <view class="filter-item {{selectedVolume ? 'selected' : ''}}" bindtap="showVolumeFilter">
        <text class="filter-text">{{selectedVolume || '册别'}}</text>
        <text class="filter-arrow {{showVolumePopup ? 'rotate' : ''}}">▼</text>
      </view>
      <view class="filter-item {{selectedCategory ? 'selected' : ''}}" bindtap="showCategoryFilter">
        <text class="filter-text">{{selectedCategory || '板块'}}</text>
        <text class="filter-arrow {{showCategoryPopup ? 'rotate' : ''}}">▼</text>
      </view>
    </view>
    <view class="filter-actions" wx:if="{{selectedGrade || selectedSubject || selectedVolume || selectedCategory}}">
      <view class="reset-btn" bindtap="resetFilters">重置筛选</view>
    </view>
  </view>

  <!-- 排序选项 -->
  <view class="sort-section">
    <view class="sort-item {{sortType === 'download' ? 'active' : ''}}" bindtap="setSortType" data-type="download">
      <text>下载量</text>
      <text class="sort-arrow" wx:if="{{sortType === 'download'}}">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
    </view>
    <view class="sort-item {{sortType === 'time' ? 'active' : ''}}" bindtap="setSortType" data-type="time">
      <text>更新时间{{sortType === 'time' ? (sortOrder === 'asc' ? '(旧→新)' : '(新→旧)') : ''}}</text>
      <text class="sort-arrow" wx:if="{{sortType === 'time'}}">{{sortOrder === 'asc' ? '↑' : '↓'}}</text>
    </view>
    <view class="sort-item {{sortType === 'points' ? 'active' : ''}}" bindtap="setSortType" data-type="points">
      <text>积分</text>
      <text class="sort-arrow" wx:if="{{sortType === 'points'}}">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
    </view>
  </view>

  <!-- 资料列表 -->
  <view class="material-list">
    <view class="list-item" wx:for="{{materialList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
      <image class="material-cover" src="{{item.cover}}" mode="aspectFill" lazy-load />
      <view class="material-info">
        <view class="material-title">{{item.title}}</view>
        <view class="material-tags">
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
        </view>
        <view class="material-meta">
          <view class="material-stats">
            <text class="stat-item">下载 {{item.downloadCount}}</text>
            <text class="stat-item">浏览 {{item.viewCount}}</text>
          </view>
          <view class="material-price">{{item.points}}积分</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && materialList.length === 0}}">
    <view class="empty-icon">📚</view>
    <view class="empty-text">暂无相关资料</view>
    <view class="empty-tip">试试调整筛选条件</view>
  </view>

  <!-- 到底提示 -->
  <view class="load-more" wx:if="{{!loading && materialList.length > 0 && !hasMore}}">
    <text class="load-more-text">已加载全部资料</text>
  </view>
</view>

<!-- 年级筛选弹窗 -->
<view class="popup-mask" wx:if="{{showGradePopup}}" bindtap="hideGradeFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择年级</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{selectedGrade === item ? 'active' : ''}}" 
              wx:for="{{gradeOptions}}" wx:key="*this" 
              bindtap="selectGrade" data-grade="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 科目筛选弹窗 -->
<view class="popup-mask" wx:if="{{showSubjectPopup}}" bindtap="hideSubjectFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择科目</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{selectedSubject === item ? 'active' : ''}}" 
              wx:for="{{subjectOptions}}" wx:key="*this" 
              bindtap="selectSubject" data-subject="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 册别筛选弹窗 -->
<view class="popup-mask" wx:if="{{showVolumePopup}}" bindtap="hideVolumeFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择册别</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{selectedVolume === item ? 'active' : ''}}" 
              wx:for="{{volumeOptions}}" wx:key="*this" 
              bindtap="selectVolume" data-volume="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 板块筛选弹窗 -->
<view class="popup-mask" wx:if="{{showCategoryPopup}}" bindtap="hideCategoryFilter">
  <view class="popup" catchtap="">
    <view class="popup-header">选择板块</view>
    <view class="popup-content">
      <view class="filter-options">
        <view class="filter-option {{selectedCategory === item ? 'active' : ''}}" 
              wx:for="{{categoryOptions}}" wx:key="*this" 
              bindtap="selectCategory" data-category="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</view>