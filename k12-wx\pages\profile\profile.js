// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: {
      isLogin: false,
      nickname: '',
      avatar: '',
      grade: '',
      points: 0,
      joinDays: 0
    },
    userStats: {
      downloadCount: 0,
      favoriteCount: 0,
      shareCount: 0
    }
  },

  onLoad() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  onShow() {
    this.loadUserInfo()
    this.loadUserStats()
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 优先从本地存储获取基本用户信息
      const localUserInfo = wx.getStorageSync('userInfo') || {}
      
      if (localUserInfo.openid) {
        // 检查是否为mock用户（降级方案用户）
        if (localUserInfo.openid.startsWith('mock_')) {
          // Mock用户直接使用本地数据，不查询云数据库
          console.log('检测到mock用户，使用本地数据')
          this.loadLocalUserInfo(localUserInfo)
        } else {
          // 真实用户从云数据库获取最新用户信息
          try {
            const result = await app.api.user.getUserInfo(localUserInfo.openid)
            if (result.success && result.data) {
              const userInfo = result.data
              // 计算加入天数
              const joinDate = userInfo.joinDate || new Date().getTime()
              const joinDays = Math.floor((new Date().getTime() - joinDate) / (1000 * 60 * 60 * 24))
              
              this.setData({
                userInfo: {
                  isLogin: true,
                  nickname: userInfo.nickname || '',
                  avatar: userInfo.avatar || '',
                  grade: userInfo.grade || '',
                  points: userInfo.points || 0,
                  joinDays: joinDays
                }
              })
              
              // 更新本地存储
              wx.setStorageSync('userInfo', userInfo)
            } else {
              // 云数据库获取失败，使用本地数据
              this.loadLocalUserInfo(localUserInfo)
            }
          } catch (error) {
            console.log('从云数据库加载用户信息失败，使用本地数据:', error)
            // 降级到本地存储
            this.loadLocalUserInfo(localUserInfo)
          }
        }
      } else {
        // 未登录状态
        this.setData({
          userInfo: {
            isLogin: false,
            nickname: '',
            avatar: '',
            grade: '',
            points: 0,
            joinDays: 0
          }
        })
      }
    } catch (error) {
      console.error('加载用户信息失败:', error)
    }
  },

  // 从本地存储加载用户信息（降级方案）
  loadLocalUserInfo(localUserInfo) {
    const points = app.getUserPoints()
    const joinDate = localUserInfo.joinDate || new Date().getTime()
    const joinDays = Math.floor((new Date().getTime() - joinDate) / (1000 * 60 * 60 * 24))
    
    this.setData({
      userInfo: {
        isLogin: !!localUserInfo.openid,
        nickname: localUserInfo.nickname || '',
        avatar: localUserInfo.avatar || '',
        grade: localUserInfo.grade || '',
        points: points,
        joinDays: joinDays
      }
    })
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const localUserInfo = wx.getStorageSync('userInfo') || {}
      
      if (localUserInfo.openid) {
        // 检查是否为mock用户（降级方案用户）
        if (localUserInfo.openid.startsWith('mock_')) {
          // Mock用户直接使用本地数据，不查询云数据库
          console.log('检测到mock用户统计，使用本地数据')
          this.loadLocalUserStats()
        } else {
          // 真实用户从云数据库获取统计数据
          try {
            const result = await app.api.user.getUserStats(localUserInfo.openid)
            if (result.success && result.data) {
              this.setData({
                userStats: result.data
              })
            } else {
              // 云数据库获取失败，使用本地数据
              this.loadLocalUserStats()
            }
          } catch (error) {
            console.log('从云数据库加载用户统计失败，使用本地数据:', error)
            // 降级到本地存储
            this.loadLocalUserStats()
          }
        }
      } else {
        // 未登录状态，重置统计数据
        this.setData({
          userStats: {
            downloadCount: 0,
            favoriteCount: 0,
            shareCount: 0
          }
        })
      }
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  // 从本地存储加载用户统计（降级方案）
  loadLocalUserStats() {
    const downloads = wx.getStorageSync('downloads') || []
    const favorites = wx.getStorageSync('favorites') || []
    const shareHistory = wx.getStorageSync('shareHistory') || []
    
    this.setData({
      userStats: {
        downloadCount: downloads.length,
        favoriteCount: favorites.length,
        shareCount: shareHistory.length
      }
    })
  },

  // 跳转到登录页面
  goToLogin() {
    if (this.data.userInfo.isLogin) return
    
    // 使用微信登录
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.performLogin(res.userInfo)
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 执行登录
  async performLogin(userInfo) {
    try {
      wx.showLoading({
        title: '登录中...'
      })
      
      // 获取登录凭证
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })
      
      // 调用云函数进行登录
      const loginData = {
        code: loginRes.code,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl
      }
      
      try {
        const result = await app.api.user.login(loginData)
        
        if (result && result.success && result.data) {
          // 登录成功，保存用户信息
          wx.setStorageSync('userInfo', result.data)
          
          // 刷新用户信息和统计数据
          await this.loadUserInfo()
          await this.loadUserStats()
          
          wx.hideLoading()
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        } else {
          throw new Error(result?.message || '登录失败')
        }
      } catch (cloudError) {
        console.log('云函数登录失败，使用降级方案:', cloudError)
        
        // 降级到本地模拟登录 - 为每个用户生成唯一的mock openid
        // 基于用户昵称和头像生成稳定的ID，确保同一用户每次登录得到相同的openid
        const mockOpenid = this.generateMockOpenid(userInfo.nickName, userInfo.avatarUrl)
        
        const mockUserInfo = {
          openid: mockOpenid,
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl,
          grade: '',
          points: 200, // 新用户赠送200积分（符合PRD要求）
          joinDate: new Date().getTime()
        }
        
        // 保存用户信息
        wx.setStorageSync('userInfo', mockUserInfo)
        
        // 初始化用户积分
        app.updateUserPoints(200)
        
        // 刷新用户信息
        await this.loadUserInfo()
        await this.loadUserStats()
        
        wx.hideLoading()
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      }
      
    } catch (error) {
      console.error('登录失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    }
  },

  // 跳转到下载页面
  goToDownloads() {
    if (!this.checkLogin()) return
    
    wx.navigateTo({
      url: '/pages/my-downloads/my-downloads'
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    if (!this.checkLogin()) return
    
    wx.navigateTo({
      url: '/pages/my-favorites/my-favorites'
    })
  },


  // 跳转到赚取积分页面
  goToEarnPoints() {
    if (!this.checkLogin()) return
    
    wx.navigateTo({
      url: '/pages/earn-points/earn-points'
    })
  },

  // 跳转到积分明细页面
  goToPointsDetail() {
    if (!this.checkLogin()) return
    
    wx.navigateTo({
      url: '/pages/points-detail/points-detail'
    })
  },


  // 跳转到意见反馈页面
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    })
  },

  // 跳转到关于我们页面
  goToAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },


  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.performLogout()
        }
      }
    })
  },

  // 执行退出登录
  performLogout() {
    try {
      // 清除用户信息
      wx.removeStorageSync('userInfo')
      
      // 重置用户数据
      this.setData({
        userInfo: {
          isLogin: false,
          nickname: '',
          avatar: '',
          grade: '',
          points: 0,
          joinDays: 0
        },
        userStats: {
          downloadCount: 0,
          favoriteCount: 0,
          shareCount: 0
        }
      })
      
      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('退出登录失败:', error)
      wx.showToast({
        title: '退出失败',
        icon: 'none'
      })
    }
  },

  // 生成mock openid（基于用户信息生成稳定的唯一ID）
  generateMockOpenid(nickname, avatarUrl) {
    // 使用用户昵称和头像URL生成一个稳定的hash
    const input = (nickname || 'anonymous') + (avatarUrl || 'default')
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    // 生成类似真实openid的格式，确保每个用户都有唯一但稳定的ID
    return 'mock_' + Math.abs(hash).toString(36) + '_' + (nickname || 'user').slice(0,3)
  },

  // 检查登录状态
  checkLogin() {
    if (!this.data.userInfo.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          this.goToLogin()
        }
      })
      return false
    }
    return true
  },

})
