// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event
  
  try {
    switch (action) {
      case 'getList':
        // 获取资源列表
        return await getResourceList(event)
      case 'getDetail':
        // 获取资源详情
        return await getResourceDetail(event)
      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('资源操作失败:', error)
    return {
      success: false,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 获取资源列表
async function getResourceList(event) {
  const { page = 1, limit = 10, category } = event
  
  let query = db.collection('resources')
  
  if (category) {
    query = query.where({
      category: category
    })
  }
  
  const result = await query
    .skip((page - 1) * limit)
    .limit(limit)
    .orderBy('createTime', 'desc')
    .get()
  
  return {
    success: true,
    data: result.data,
    total: result.data.length
  }
}

// 获取资源详情
async function getResourceDetail(event) {
  const { resourceId } = event
  
  const result = await db.collection('resources').doc(resourceId).get()
  
  if (result.data) {
    return {
      success: true,
      data: result.data
    }
  } else {
    return {
      success: false,
      message: '资源不存在'
    }
  }
}