import { initDefaultCategories } from '@/api/category'

// 初始化分类数据的工具函数
export const setupDefaultCategories = async () => {
  try {
    console.log('开始初始化默认分类数据...')
    const result = await initDefaultCategories()
    
    if (result.success) {
      console.log('✅ 分类数据初始化成功:', result.message)
      return true
    } else {
      console.error('❌ 分类数据初始化失败:', result.message)
      return false
    }
  } catch (error) {
    console.error('❌ 初始化分类数据时发生错误:', error)
    return false
  }
}