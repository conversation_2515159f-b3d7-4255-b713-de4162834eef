/* 资料详情页样式 - 现代化设计 + 微交互动画增强 */

.container {
  padding-bottom: 160rpx;
  background: linear-gradient(180deg, #f8faff 0%, #ffffff 100%);
  min-height: 100vh;
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 微交互动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 资料信息 - 现代卡片设计 + 微交互动画 */
.material-info {
  background: #ffffff;
  padding: 32rpx;
  margin: 24rpx 24rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.material-info::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.material-info:active::after {
  width: 400rpx;
  height: 400rpx;
}

.material-cover {
  width: 100%;
  height: 400rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(22, 119, 255, 0.15);
  animation: fadeInScale 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.material-cover:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 24rpx rgba(22, 119, 255, 0.2);
}

.material-cover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(22, 119, 255, 0.1) 0%, rgba(105, 177, 255, 0.05) 100%);
  pointer-events: none;
}

.info-content {
  position: relative;
  z-index: 1;
}

.material-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.material-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 32rpx;
}

.material-tags .tag:nth-child(1) { animation-delay: 0.4s; }
.material-tags .tag:nth-child(2) { animation-delay: 0.5s; }
.material-tags .tag:nth-child(3) { animation-delay: 0.6s; }
.material-tags .tag:nth-child(4) { animation-delay: 0.7s; }
.material-tags .tag:nth-child(5) { animation-delay: 0.8s; }

.material-tags .tag {
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateX(-20px);
  position: relative;
  overflow: hidden;
}

.material-tags .tag::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

.material-tags .tag:active::after {
  width: 120rpx;
  height: 120rpx;
}

.material-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.05) 0%, rgba(105, 177, 255, 0.02) 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(22, 119, 255, 0.1);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: scale(0.9);
}

.stat-item:nth-child(1) { animation-delay: 0.6s; }
.stat-item:nth-child(2) { animation-delay: 0.7s; }
.stat-item:nth-child(3) { animation-delay: 0.8s; }

.stat-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

.stat-item:active::after {
  width: 160rpx;
  height: 160rpx;
}

.stat-item:not(:last-child)::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 40rpx;
  background: linear-gradient(180deg, transparent 0%, rgba(22, 119, 255, 0.2) 50%, transparent 100%);
}

.stat-label {
  font-size: 24rpx;
  color: #8a8a8a;
  margin-bottom: 8rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #1677FF;
  text-shadow: 0 1rpx 2rpx rgba(22, 119, 255, 0.1);
  position: relative;
  z-index: 1;
}

.material-price {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(255, 107, 53, 0.3);
  position: relative;
  overflow: hidden;
  animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.7s both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.material-price::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.material-price::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.5s ease, height 0.5s ease;
  pointer-events: none;
  z-index: 1;
}

.material-price:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
}

.material-price:active::before {
  left: 100%;
}

.material-price:active::after {
  width: 240rpx;
  height: 240rpx;
}

.price-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 12rpx;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.price-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

/* 资料描述 - 现代卡片设计 + 微交互动画 */
.material-description {
  background: #ffffff;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
  position: relative;
  overflow: hidden;
}

.material-description::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.08) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  pointer-events: none;
  z-index: 0;
}

.material-description:active::after {
  width: 360rpx;
  height: 360rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  position: relative;
  z-index: 1;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #1677FF 0%, #69B1FF 100%);
  border-radius: 2rpx;
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.description-content {
  font-size: 28rpx;
  color: #4a4a4a;
  line-height: 1.7;
  text-align: justify;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

/* 资料预览 - 现代滚动卡片 + 微交互动画 */
.material-preview {
  background: #ffffff;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.preview-scroll {
  width: 100%;
  white-space: nowrap;
}

.preview-list {
  display: inline-flex;
  gap: 24rpx;
  padding: 8rpx 0;
}

.preview-image {
  width: 240rpx;
  height: 320rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(22, 119, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: scale(0.9);
}

.preview-image:nth-child(1) { animation-delay: 0.8s; }
.preview-image:nth-child(2) { animation-delay: 0.9s; }
.preview-image:nth-child(3) { animation-delay: 1.0s; }
.preview-image:nth-child(4) { animation-delay: 1.1s; }

.preview-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(22, 119, 255, 0.1) 0%, rgba(105, 177, 255, 0.05) 100%);
  pointer-events: none;
}

.preview-image::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 1;
}

.preview-image:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.2);
}

.preview-image:active::after {
  width: 200rpx;
  height: 200rpx;
}

/* 相关推荐 - 现代列表设计 + 微交互动画 */
.related-materials {
  background: #ffffff;
  padding: 32rpx;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(22, 119, 255, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.related-item {
  display: flex;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.02) 0%, rgba(255, 255, 255, 1) 100%);
  border-radius: 20rpx;
  border: 1rpx solid rgba(22, 119, 255, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(20px);
}

.related-item:nth-child(1) { animation-delay: 1.2s; }
.related-item:nth-child(2) { animation-delay: 1.3s; }
.related-item:nth-child(3) { animation-delay: 1.4s; }
.related-item:nth-child(4) { animation-delay: 1.5s; }

.related-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(180deg, #1677FF 0%, #69B1FF 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.related-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.5s ease, height 0.5s ease;
  pointer-events: none;
  z-index: 0;
}

.related-item:active {
  background: linear-gradient(135deg, rgba(22, 119, 255, 0.08) 0%, rgba(255, 255, 255, 1) 100%);
  transform: translateX(8rpx);
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.15);
}

.related-item:active::before {
  width: 6rpx;
}

.related-item:active::after {
  width: 300rpx;
  height: 300rpx;
}

.related-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.related-cover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(22, 119, 255, 0.1) 0%, rgba(105, 177, 255, 0.05) 100%);
  pointer-events: none;
}

.related-cover:active {
  transform: scale(0.95);
}

.related-info {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.related-title {
  font-size: 28rpx;
  color: #1a1a1a;
  line-height: 1.4;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.related-price {
  font-size: 26rpx;
  font-weight: 700;
  color: #FF6B35;
  align-self: flex-end;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.05) 100%);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.related-price::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 107, 53, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

.related-price:active::after {
  width: 100rpx;
  height: 100rpx;
}

/* 底部操作栏 - 现代化设计 + 微交互动画 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(22, 119, 255, 0.1);
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 1.6s both;
}

.action-left {
  display: flex;
  gap: 40rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(22, 119, 255, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
  pointer-events: none;
  z-index: 0;
}

.action-btn:active {
  background: rgba(22, 119, 255, 0.1);
  transform: scale(0.9);
}

.action-btn:active::after {
  width: 160rpx;
  height: 160rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #1677FF 0%, #69B1FF 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(22, 119, 255, 0.3);
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:active .action-icon {
  transform: scale(0.9);
}

.action-text {
  font-size: 22rpx;
  color: #4a4a4a;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.download-btn {
  flex: 1;
  margin-left: 32rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1677FF 0%, #4285F4 100%);
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6rpx 24rpx rgba(22, 119, 255, 0.4);
  position: relative;
  overflow: hidden;
}

.download-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.download-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.5s ease, height 0.5s ease;
  pointer-events: none;
  z-index: 1;
}

.download-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(22, 119, 255, 0.5);
}

.download-btn:active::before {
  left: 100%;
}

.download-btn:active::after {
  width: 300rpx;
  height: 300rpx;
}

.download-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #FFFFFF;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

/* 加载和空状态 - 现代化设计 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #8a8a8a;
  font-size: 28rpx;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.loading::before {
  content: '';
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(22, 119, 255, 0.2);
  border-top: 4rpx solid #1677FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #8a8a8a;
  font-size: 28rpx;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

.empty-text {
  line-height: 1.5;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .material-stats {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .stat-item:not(:last-child)::before {
    display: none;
  }
  
  .related-item {
    flex-direction: column;
    text-align: center;
  }
  
  .related-info {
    margin-left: 0;
    margin-top: 16rpx;
  }
}

/* 性能优化 */
.container,
.material-info,
.material-description,
.material-preview,
.related-materials,
.bottom-actions {
  will-change: transform, opacity;
}

.material-cover,
.preview-image,
.related-item {
  will-change: transform, box-shadow;
}
