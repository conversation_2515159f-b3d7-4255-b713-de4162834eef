// pages/my-favorites/my-favorites.js
const app = getApp()

Page({
  data: {
    favorites: [],
    loading: false,
    isEmpty: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    sortType: 'time', // time: 按时间排序, name: 按名称排序
    filterGrade: '', // 年级筛选
    filterSubject: '', // 学科筛选
    grades: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三'],
    subjects: ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '科学', '道德与法治'],
    editMode: false,
    selectedItems: []
  },

  onLoad() {
    this.loadFavorites()
  },

  onShow() {
    this.loadFavorites()
  },

  // 加载收藏记录
  async loadFavorites(refresh = false) {
    if (this.data.loading) return
    
    // 如果是刷新，重置分页
    if (refresh) {
      this.setData({
        page: 1,
        favorites: [],
        hasMore: true
      })
    }
    
    this.setData({ loading: true })
    
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      const result = await app.api.favorite.getFavoriteList({
        userId: userInfo._id,
        page: this.data.page,
        pageSize: this.data.pageSize,
        grade: this.data.filterGrade,
        subject: this.data.filterSubject
      })
      
      let newFavorites = refresh ? result.favorites : [...this.data.favorites, ...result.favorites]
      
      this.setData({
        favorites: newFavorites,
        isEmpty: newFavorites.length === 0,
        hasMore: result.hasMore,
        page: refresh ? 2 : this.data.page + 1,
        loading: false
      })
      
    } catch (error) {
      console.error('加载收藏记录失败:', error)
      
      // 使用本地存储作为降级处理
      this.loadLocalFavorites()
      
      wx.showToast({
        title: '加载失败，显示本地数据',
        icon: 'none'
      })
    }
  },

  // 本地存储降级处理
  loadLocalFavorites() {
    try {
      const favorites = wx.getStorageSync('favorites') || []
      favorites.sort((a, b) => new Date(b.favoriteTime) - new Date(a.favoriteTime))
      
      this.setData({
        favorites: favorites,
        isEmpty: favorites.length === 0,
        loading: false,
        hasMore: false
      })
    } catch (error) {
      console.error('加载本地收藏失败:', error)
      this.setData({
        loading: false,
        isEmpty: true
      })
    }
  },

  // 排序切换
  onSortChange(e) {
    const sortType = e.detail.value
    this.setData({ sortType })
    this.sortFavorites(sortType)
  },

  // 排序收藏记录
  sortFavorites(sortType) {
    let favorites = [...this.data.favorites]
    
    if (sortType === 'time') {
      favorites.sort((a, b) => new Date(b.favoriteTime) - new Date(a.favoriteTime))
    } else if (sortType === 'name') {
      favorites.sort((a, b) => a.title.localeCompare(b.title))
    }
    
    this.setData({ favorites })
  },

  // 年级筛选
  onGradeFilter() {
    const grades = ['全部', ...this.data.grades]
    
    wx.showActionSheet({
      itemList: grades,
      success: (res) => {
        const selectedGrade = res.tapIndex === 0 ? '' : grades[res.tapIndex]
        this.setData({ filterGrade: selectedGrade })
        this.filterFavorites()
      }
    })
  },

  // 学科筛选
  onSubjectFilter() {
    const subjects = ['全部', ...this.data.subjects]
    
    wx.showActionSheet({
      itemList: subjects,
      success: (res) => {
        const selectedSubject = res.tapIndex === 0 ? '' : subjects[res.tapIndex]
        this.setData({ filterSubject: selectedSubject })
        this.filterFavorites()
      }
    })
  },

  // 筛选收藏记录
  filterFavorites() {
    // 重新加载数据，云函数会处理筛选
    this.loadFavorites(true)
  },

  // 清空筛选
  clearFilter() {
    this.setData({
      filterGrade: '',
      filterSubject: ''
    })
    this.loadFavorites()
  },

  // 切换编辑模式
  toggleEditMode() {
    this.setData({
      editMode: !this.data.editMode,
      selectedItems: []
    })
  },

  // 选择项目
  toggleSelectItem(e) {
    const { id } = e.currentTarget.dataset
    let selectedItems = [...this.data.selectedItems]
    
    const index = selectedItems.indexOf(id)
    if (index > -1) {
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(id)
    }
    
    this.setData({ selectedItems })
  },

  // 全选/取消全选
  toggleSelectAll() {
    const allSelected = this.data.selectedItems.length === this.data.favorites.length
    
    if (allSelected) {
      this.setData({ selectedItems: [] })
    } else {
      const allIds = this.data.favorites.map(item => item.id)
      this.setData({ selectedItems: allIds })
    }
  },

  // 查看资料详情
  viewMaterial(e) {
    if (this.data.editMode) return
    
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/material-detail/material-detail?id=${id}`
    })
  },

  // 取消收藏
  removeFavorite(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消收藏这个资料吗？',
      success: (res) => {
        if (res.confirm) {
          this.performRemoveFavorite(id)
        }
      }
    })
  },

  // 执行取消收藏
  async performRemoveFavorite(materialId) {
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      // 调用云函数取消收藏
      await app.api.favorite.removeFavorite(materialId)
      
      // 从本地列表中移除
      let favorites = this.data.favorites.filter(item => 
        item.materialId !== materialId && item.material && item.material._id !== materialId
      )
      
      this.setData({
        favorites: favorites,
        isEmpty: favorites.length === 0
      })
      
      wx.showToast({
        title: '取消收藏成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('取消收藏失败:', error)
      
      // 降级到本地存储处理
      try {
        let localFavorites = wx.getStorageSync('favorites') || []
        localFavorites = localFavorites.filter(item => item.id !== materialId)
        wx.setStorageSync('favorites', localFavorites)
        
        let favorites = this.data.favorites.filter(item => 
          item.id !== materialId && item.materialId !== materialId
        )
        
        this.setData({
          favorites: favorites,
          isEmpty: favorites.length === 0
        })
        
        wx.showToast({
          title: '取消收藏成功',
          icon: 'success'
        })
      } catch (localError) {
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  },

  // 批量删除选中项
  deleteSelected() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的项目',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的 ${this.data.selectedItems.length} 个收藏吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performBatchDelete()
        }
      }
    })
  },

  // 执行批量删除
  async performBatchDelete() {
    try {
      const userInfo = app.globalData.userInfo
      if (!userInfo || !userInfo._id) {
        throw new Error('用户信息不存在')
      }
      
      // 获取要删除的资料ID列表
      const materialIds = this.data.selectedItems.map(itemId => {
        const favorite = this.data.favorites.find(fav => 
          fav.id === itemId || fav._id === itemId || fav.materialId === itemId
        )
        return favorite ? (favorite.materialId || favorite.material?._id || favorite.id) : itemId
      })
      
      // 调用云函数批量删除
      await app.api.favorite.batchRemoveFavorite(materialIds)
      
      // 从本地列表中移除
      let favorites = this.data.favorites.filter(item => 
        !this.data.selectedItems.includes(item.id) && 
        !this.data.selectedItems.includes(item._id) &&
        !this.data.selectedItems.includes(item.materialId)
      )
      
      this.setData({
        favorites: favorites,
        isEmpty: favorites.length === 0,
        editMode: false,
        selectedItems: []
      })
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('批量删除失败:', error)
      
      // 降级到本地存储处理
      try {
        let favorites = wx.getStorageSync('favorites') || []
        favorites = favorites.filter(item => !this.data.selectedItems.includes(item.id))
        wx.setStorageSync('favorites', favorites)
        
        let localFavorites = this.data.favorites.filter(item => 
          !this.data.selectedItems.includes(item.id) && 
          !this.data.selectedItems.includes(item._id)
        )
        
        this.setData({
          favorites: localFavorites,
          isEmpty: localFavorites.length === 0,
          editMode: false,
          selectedItems: []
        })
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } catch (localError) {
        wx.showToast({
          title: '删除失败',
          icon: 'none'
        })
      }
    }
  },

  // 清空所有收藏
  clearAll() {
    if (this.data.favorites.length === 0) return
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有收藏吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('favorites')
            this.setData({
              favorites: [],
              isEmpty: true,
              editMode: false,
              selectedItems: []
            })
            
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('清空失败:', error)
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadFavorites(true)
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadFavorites()
    }
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的收藏 - K12教育资料库',
      path: '/pages/index/index'
    }
  }
})