import { db } from './index'

// 权限接口
export interface Permission {
  _id?: string
  name: string
  code: string
  type: 'menu' | 'button' | 'api'
  category: string
  parentId?: string
  path?: string
  icon?: string
  sort: number
  status: 'active' | 'inactive'
  description?: string
  createTime?: Date
  updateTime?: Date
}

// 角色接口
export interface Role {
  _id?: string
  name: string
  code: string
  description?: string
  permissions: string[]
  status: 'active' | 'inactive'
  createTime?: Date
  updateTime?: Date
}

// 用户角色关联接口
export interface UserRole {
  _id?: string
  userId: string
  roleId: string
  createTime?: Date
}

// API响应格式
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  total?: number
  page?: number
  pageSize?: number
}

// 权限管理API
export const permissionApi = {
  // 获取权限列表
  async getList(params: {
    page?: number
    pageSize?: number
    type?: string
    status?: string
    keyword?: string
  } = {}): Promise<ApiResponse<Permission[]>> {
    try {
      let query = db.collection('permissions')
      
      // 添加筛选条件
      const conditions: any = {}
      if (params.type) {
        conditions.type = params.type
      }
      if (params.status) {
        conditions.status = params.status
      }
      if (params.keyword) {
        conditions.name = new RegExp(params.keyword, 'i')
      }
      
      if (Object.keys(conditions).length > 0) {
        query = query.where(conditions)
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const skip = (page - 1) * pageSize
      
      const countResult = await query.count()
      const result = await query.orderBy('sort', 'asc').skip(skip).limit(pageSize).get()
      
      return {
        success: true,
        data: result.data,
        total: countResult.total,
        page,
        pageSize
      }
    } catch (error: any) {
      console.error('获取权限列表失败:', error)
      return {
        success: false,
        message: '获取权限列表失败: ' + error.message
      }
    }
  },

  // 创建权限
  async createPermission(permission: Omit<Permission, '_id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<Permission>> {
    try {
      const now = new Date()
      const newPermission = {
        ...permission,
        createTime: now,
        updateTime: now
      }
      
      const result = await db.collection('permissions').add(newPermission)
      
      return {
        success: true,
        data: { _id: result.id, ...newPermission }
      }
    } catch (error: any) {
      console.error('创建权限失败:', error)
      return {
        success: false,
        message: '创建权限失败: ' + error.message
      }
    }
  },

  // 更新权限
  async updatePermission(id: string, permission: Partial<Permission>): Promise<ApiResponse<Permission>> {
    try {
      const updateData = {
        ...permission,
        updateTime: new Date()
      }
      
      await db.collection('permissions').doc(id).update(updateData)
      
      return {
        success: true,
        data: { _id: id, ...updateData } as Permission
      }
    } catch (error: any) {
      console.error('更新权限失败:', error)
      return {
        success: false,
        message: '更新权限失败: ' + error.message
      }
    }
  },

  // 删除权限
  async deletePermission(id: string): Promise<ApiResponse> {
    try {
      await db.collection('permissions').doc(id).remove()
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('删除权限失败:', error)
      return {
        success: false,
        message: '删除权限失败: ' + error.message
      }
    }
  },

  // 批量删除权限
  async batchDeletePermissions(ids: string[]): Promise<ApiResponse> {
    try {
      // 使用Promise.all并发删除
      const deletePromises = ids.map(id => 
        db.collection('permissions').doc(id).remove()
      )
      
      await Promise.all(deletePromises)
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('批量删除权限失败:', error)
      return {
        success: false,
        message: '批量删除权限失败: ' + error.message
      }
    }
  },

  // 获取权限树
  async getPermissionTree(): Promise<ApiResponse<Permission[]>> {
    try {
      const result = await db.collection('permissions')
        .where({ status: 'active' })
        .orderBy('sort', 'asc')
        .get()
      
      return {
        success: true,
        data: result.data
      }
    } catch (error: any) {
      console.error('获取权限树失败:', error)
      return {
        success: false,
        message: '获取权限树失败: ' + error.message
      }
    }
  }
}

// 角色管理API
export const roleApi = {
  // 获取角色列表
  async getList(params: {
    page?: number
    pageSize?: number
    status?: string
    keyword?: string
  } = {}): Promise<ApiResponse<Role[]>> {
    try {
      let query = db.collection('roles')
      
      // 添加筛选条件
      const conditions: any = {}
      if (params.status) {
        conditions.status = params.status
      }
      if (params.keyword) {
        conditions.name = new RegExp(params.keyword, 'i')
      }
      
      if (Object.keys(conditions).length > 0) {
        query = query.where(conditions)
      }
      
      // 分页
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const skip = (page - 1) * pageSize
      
      const countResult = await query.count()
      const result = await query.orderBy('createTime', 'desc').skip(skip).limit(pageSize).get()
      
      return {
        success: true,
        data: result.data,
        total: countResult.total,
        page,
        pageSize
      }
    } catch (error: any) {
      console.error('获取角色列表失败:', error)
      return {
        success: false,
        message: '获取角色列表失败: ' + error.message
      }
    }
  },

  // 创建角色
  async createRole(role: Omit<Role, '_id' | 'createTime' | 'updateTime'>): Promise<ApiResponse<Role>> {
    try {
      const now = new Date()
      const newRole = {
        ...role,
        createTime: now,
        updateTime: now
      }
      
      const result = await db.collection('roles').add(newRole)
      
      return {
        success: true,
        data: { _id: result.id, ...newRole }
      }
    } catch (error: any) {
      console.error('创建角色失败:', error)
      return {
        success: false,
        message: '创建角色失败: ' + error.message
      }
    }
  },

  // 更新角色
  async updateRole(id: string, role: Partial<Role>): Promise<ApiResponse<Role>> {
    try {
      const updateData = {
        ...role,
        updateTime: new Date()
      }
      
      await db.collection('roles').doc(id).update(updateData)
      
      return {
        success: true,
        data: { _id: id, ...updateData } as Role
      }
    } catch (error: any) {
      console.error('更新角色失败:', error)
      return {
        success: false,
        message: '更新角色失败: ' + error.message
      }
    }
  },

  // 删除角色
  async deleteRole(id: string): Promise<ApiResponse> {
    try {
      await db.collection('roles').doc(id).remove()
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('删除角色失败:', error)
      return {
        success: false,
        message: '删除角色失败: ' + error.message
      }
    }
  },

  // 批量删除角色
  async batchDeleteRoles(ids: string[]): Promise<ApiResponse> {
    try {
      // 使用Promise.all并发删除
      const deletePromises = ids.map(id => 
        db.collection('roles').doc(id).remove()
      )
      
      await Promise.all(deletePromises)
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('批量删除角色失败:', error)
      return {
        success: false,
        message: '批量删除角色失败: ' + error.message
      }
    }
  },

  // 分配权限给角色
  async assignPermissions(roleId: string, permissionIds: string[]): Promise<ApiResponse> {
    try {
      await db.collection('roles').doc(roleId).update({
        permissions: permissionIds,
        updateTime: new Date()
      })
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('分配权限失败:', error)
      return {
        success: false,
        message: '分配权限失败: ' + error.message
      }
    }
  }
}

// 用户角色管理API
export const userRoleApi = {
  // 获取用户角色列表
  async getUserRoles(userId: string): Promise<ApiResponse<UserRole[]>> {
    try {
      const result = await db.collection('user_roles')
        .where({ userId })
        .get()
      
      return {
        success: true,
        data: result.data
      }
    } catch (error: any) {
      console.error('获取用户角色失败:', error)
      return {
        success: false,
        message: '获取用户角色失败: ' + error.message
      }
    }
  },

  // 分配角色给用户
  async assignRole(userId: string, roleId: string): Promise<ApiResponse<UserRole>> {
    try {
      // 检查是否已存在
      const existingResult = await db.collection('user_roles')
        .where({ userId, roleId })
        .get()
      
      if (existingResult.data.length > 0) {
        return {
          success: false,
          message: '用户已拥有该角色'
        }
      }
      
      const newUserRole = {
        userId,
        roleId,
        createTime: new Date()
      }
      
      const result = await db.collection('user_roles').add(newUserRole)
      
      return {
        success: true,
        data: { _id: result.id, ...newUserRole }
      }
    } catch (error: any) {
      console.error('分配角色失败:', error)
      return {
        success: false,
        message: '分配角色失败: ' + error.message
      }
    }
  },

  // 移除用户角色
  async removeRole(userId: string, roleId: string): Promise<ApiResponse> {
    try {
      const result = await db.collection('user_roles')
        .where({ userId, roleId })
        .get()
      
      if (result.data.length === 0) {
        return {
          success: false,
          message: '用户角色关联不存在'
        }
      }
      
      await db.collection('user_roles').doc(result.data[0]._id).remove()
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('移除用户角色失败:', error)
      return {
        success: false,
        message: '移除用户角色失败: ' + error.message
      }
    }
  },

  // 批量分配角色
  async batchAssignRoles(assignments: { userId: string; roleIds: string[] }[]): Promise<ApiResponse> {
    try {
      const now = new Date()
      
      // 先删除现有的用户角色关联
      for (const assignment of assignments) {
        const existingResult = await db.collection('user_roles')
          .where({ userId: assignment.userId })
          .get()
        
        // 删除现有关联
        const deletePromises = existingResult.data.map((role: any) => 
          db.collection('user_roles').doc(role._id).remove()
        )
        await Promise.all(deletePromises)
        
        // 添加新的关联
        const addPromises = assignment.roleIds.map(roleId => 
          db.collection('user_roles').add({
            userId: assignment.userId,
            roleId,
            createTime: now
          })
        )
        await Promise.all(addPromises)
      }
      
      return {
        success: true
      }
    } catch (error: any) {
      console.error('批量分配角色失败:', error)
      return {
        success: false,
        message: '批量分配角色失败: ' + error.message
      }
    }
  }
}