<view class="container {{upgradeType === 'primary' ? 'primary-theme' : ''}}">
  <!-- 顶部导航 -->
  <view class="header-section">
    <view class="page-title">{{pageTitle}}</view>
    <view class="page-subtitle">{{pageSubtitle}}</view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 分类筛选 -->
    <view class="filter-section">
      <view class="filter-tabs">
        <view class="filter-tab {{activeTab === item.key ? 'active' : ''}}" 
              wx:for="{{filterTabs}}" 
              wx:key="key" 
              bindtap="switchTab" 
              data-key="{{item.key}}">
          <text class="tab-text">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 资料列表 -->
    <view class="material-section">
      <view class="material-list">
        <view class="material-item" 
              wx:for="{{materialList}}" 
              wx:key="id" 
              bindtap="goToDetail" 
              data-id="{{item.id}}">
          <view class="material-cover">
            <image wx:if="{{item.cover}}" src="{{item.cover}}" mode="aspectFill" />
            <view wx:else class="cover-placeholder">{{item.subject}}</view>
          </view>
          <view class="material-info">
            <view class="material-title">{{item.title}}</view>
            <view class="material-tags">
              <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
            </view>
            <view class="material-bottom">
              <view class="material-stats">
                <text class="stat-item">⬇ {{item.downloadCount}}</text>
                <text class="stat-item">👁 {{item.viewCount}}</text>
              </view>
              <view class="material-price">{{item.points}}积分</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && materialList.length === 0}}">
      <view class="empty-icon">📚</view>
      <view class="empty-text">暂无{{pageTitle}}资料</view>
    </view>
  </view>
</view>